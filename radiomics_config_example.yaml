# 肺结节影像组学特征提取配置文件
# 这是一个示例配置文件，用于配置PyRadiomics特征提取参数

# PyRadiomics配置
radiomics:
  # 图像类型配置
  # 可以启用多种图像滤波器来增强特征提取
  imageTypes:
    # 原始图像
    Original: {}
    
    # 拉普拉斯高斯滤波器 (LoG)
    # 用于检测不同尺度的结构
    LoG:
      sigma: [2.0, 3.0, 4.0, 5.0]  # 不同的sigma值
    
    # 小波变换
    # 用于多尺度分析
    Wavelet:
      start_level: 0
      level: 1
      wavelet: 'coif1'  # 小波类型: coif1, db1, haar等
    
    # 平方滤波器（可选）
    # Square: {}
    
    # 平方根滤波器（可选）
    # SquareRoot: {}
    
    # 对数滤波器（可选）
    # Logarithm: {}
    
    # 指数滤波器（可选）
    # Exponential: {}
  
  # 特征类别配置
  # 启用需要提取的特征类别
  featureClasses:
    # 一阶统计特征 (First Order Statistics)
    # 描述图像强度分布的基本统计特征
    firstorder: []
    
    # 三维形状特征 (Shape-based 3D)
    # 描述结节的几何形状特征
    shape: []
    
    # 灰度共生矩阵 (Gray Level Co-occurrence Matrix)
    # 描述像素间的空间关系
    glcm: []
    
    # 灰度游程矩阵 (Gray Level Run Length Matrix)
    # 描述相同灰度值的连续像素
    glrlm: []
    
    # 灰度大小区域矩阵 (Gray Level Size Zone Matrix)
    # 描述连通区域的大小分布
    glszm: []
    
    # 邻域灰度差矩阵 (Neighboring Gray Tone Difference Matrix)
    # 描述局部灰度变化
    ngtdm: []
    
    # 灰度依赖矩阵 (Gray Level Dependence Matrix)
    # 描述像素间的依赖关系
    gldm: []
  
  # PyRadiomics设置参数
  settings:
    # 灰度级量化
    binWidth: 25  # 灰度级宽度
    
    # 重采样参数
    resampledPixelSpacing: [1, 1, 1]  # 重采样像素间距 [x, y, z]
    interpolator: 'sitkBSpline'  # 插值方法: sitkNearestNeighbor, sitkLinear, sitkBSpline
    
    # 归一化设置
    normalize: true  # 是否进行归一化
    normalizeScale: 100  # 归一化尺度
    
    # 异常值处理
    removeOutliers: 3  # 移除超过N个标准差的异常值
    
    # 其他设置
    # force2D: false  # 是否强制2D分析
    # force2Ddimension: 0  # 2D分析的维度
    # correctMask: false  # 是否校正掩码
    # label: 1  # 掩码标签值
    
    # 距离权重设置（用于某些纹理特征）
    # distances: [1]  # 像素距离
    
    # 几何设置
    # geometryTolerance: 1e-3  # 几何容差
    
    # 边界处理
    # padDistance: 5  # 填充距离

# 处理参数配置
processing:
  # 并行处理
  n_jobs: 4  # 并行进程数，-1表示使用所有CPU核心
  
  # 输出格式
  output_format: ['csv', 'json']  # 支持的格式: csv, json, excel
  
  # 质量控制
  quality_check: true  # 是否进行质量检查
  min_voxel_count: 10  # 最小体素数量阈值
  
  # 日志设置
  log_level: 'INFO'  # 日志级别: DEBUG, INFO, WARNING, ERROR
  
  # 内存管理
  # max_memory_usage: '8GB'  # 最大内存使用量
  
  # 缓存设置
  # enable_cache: true  # 是否启用缓存
  # cache_dir: './cache'  # 缓存目录

# 高级配置（可选）
advanced:
  # 特征选择
  feature_selection:
    # 是否启用特征选择
    enabled: false
    
    # 选择方法
    method: 'variance_threshold'  # variance_threshold, correlation_threshold
    
    # 方差阈值
    variance_threshold: 0.01
    
    # 相关性阈值
    correlation_threshold: 0.95
  
  # 数据验证
  validation:
    # 检查图像和掩码的一致性
    check_image_mask_consistency: true
    
    # 检查掩码的连通性
    check_mask_connectivity: true
    
    # 检查图像质量
    check_image_quality: true
  
  # 错误处理
  error_handling:
    # 遇到错误时是否继续处理其他结节
    continue_on_error: true
    
    # 是否保存错误日志
    save_error_log: true
    
    # 错误日志文件名
    error_log_file: 'extraction_errors.log'

# 输出配置
output:
  # 文件命名
  naming:
    # 特征文件前缀
    feature_file_prefix: 'radiomics_features'
    
    # 是否包含时间戳
    include_timestamp: false
    
    # 时间戳格式
    timestamp_format: '%Y%m%d_%H%M%S'
  
  # 数据格式
  data_format:
    # CSV设置
    csv:
      encoding: 'utf-8-sig'  # 编码格式
      separator: ','  # 分隔符
      decimal: '.'  # 小数点符号
    
    # JSON设置
    json:
      indent: 2  # 缩进空格数
      ensure_ascii: false  # 是否确保ASCII编码
  
  # 报告生成
  reports:
    # 是否生成特征描述报告
    generate_feature_description: true
    
    # 是否生成质量评估报告
    generate_quality_report: true
    
    # 是否生成可视化图表
    generate_visualization: false