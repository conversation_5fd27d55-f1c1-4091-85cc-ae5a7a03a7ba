#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肺结节影像组学特征提取器

这是一个独立的影像组学特征提取脚本，用于处理肺结节三维分割结果
并使用PyRadiomics库提取影像组学特征。

作者: AI Assistant
日期: 2024
"""

import os
import json
import argparse
import warnings
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

import numpy as np
import pandas as pd
import nibabel as nib
from tqdm import tqdm
from loguru import logger
import yaml

# PyRadiomics相关导入
try:
    import radiomics
    from radiomics import featureextractor
    import SimpleITK as sitk
    
    # 猴子补丁：禁用SimpleITK全局容差设置
    # 这是解决坐标容差错误的关键修复
    def _dummy_set_tolerance(*args, **kwargs):
        pass
    
    # 应用猴子补丁前检查属性是否存在
    if hasattr(sitk.ProcessObject, 'SetGlobalDefaultCoordinateTolerance'):
        sitk.ProcessObject.SetGlobalDefaultCoordinateTolerance = _dummy_set_tolerance
    if hasattr(sitk.ProcessObject, 'SetGlobalDefaultDirectionTolerance'):
        sitk.ProcessObject.SetGlobalDefaultDirectionTolerance = _dummy_set_tolerance
    
    RADIOMICS_AVAILABLE = True
except ImportError:
    RADIOMICS_AVAILABLE = False
    logger.warning("PyRadiomics或SimpleITK未安装，请先安装相关依赖")

warnings.filterwarnings('ignore')


class RadiomicsFeatureExtractor:
    """
    影像组学特征提取器
    
    使用PyRadiomics库从分割的肺结节中提取影像组学特征
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化特征提取器
        
        Args:
            config_file: 配置文件路径
        """
        if not RADIOMICS_AVAILABLE:
            raise ImportError("PyRadiomics和SimpleITK是必需的依赖，请先安装")
        
        self.config = self._load_config(config_file)
        self.extractor = self._setup_extractor()
        
        # 设置日志
        logger.remove()
        logger.add(
            lambda msg: print(msg, end=""),
            level=self.config.get('processing', {}).get('log_level', 'INFO')
        )
    
    def _load_config(self, config_file: Optional[str]) -> Dict:
        """
        加载配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            Dict: 配置字典
        """
        default_config = {
            'radiomics': {
                'imageTypes': {
                    'Original': {},
                    'LoG': {'sigma': [2.0, 3.0, 4.0, 5.0]},
                    'Wavelet': {
                        'start_level': 0,
                        'level': 1,
                        'wavelet': 'coif1'
                    }
                },
                'featureClasses': {
                    'firstorder': [],
                    'shape': [],
                    'glcm': [],
                    'glrlm': [],
                    'glszm': [],
                    'ngtdm': [],
                    'gldm': []
                },
                'settings': {
                    'binWidth': 25,
                    'resampledPixelSpacing': [1, 1, 1],
                    'interpolator': 'sitkBSpline',
                    'normalize': True,
                    'normalizeScale': 100,
                    'removeOutliers': 3
                }
            },
            'processing': {
                'n_jobs': 4,
                'output_format': ['csv', 'json'],
                'quality_check': True,
                'min_voxel_count': 10,
                'log_level': 'INFO'
            }
        }
        
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                    user_config = yaml.safe_load(f)
                else:
                    user_config = json.load(f)
            
            # 合并配置
            default_config.update(user_config)
        
        return default_config
    
    def _setup_extractor(self) -> featureextractor.RadiomicsFeatureExtractor:
        """
        设置PyRadiomics特征提取器
        
        Returns:
            RadiomicsFeatureExtractor: 配置好的特征提取器
        """
        # 禁用SimpleITK容差设置以避免版本兼容性问题
        logger.info("已禁用SimpleITK全局容差设置以避免版本兼容性问题")
        
        try:
            # 创建特征提取器
            extractor = featureextractor.RadiomicsFeatureExtractor()
            
            # 设置图像类型
            image_types = self.config['radiomics']['imageTypes']
            for img_type, params in image_types.items():
                extractor.enableImageTypeByName(img_type, **params)
            
            # 设置特征类别
            feature_classes = self.config['radiomics']['featureClasses']
            for feature_class, params in feature_classes.items():
                if isinstance(params, list) and len(params) == 0:
                    extractor.enableFeatureClassByName(feature_class)
                else:
                    extractor.enableFeatureClassByName(feature_class, **params)
            
            # 设置参数 - 修正版本兼容性问题
            settings = self.config['radiomics']['settings'].copy()
            # 确保不包含容差相关设置
            if 'geometryTolerance' in settings:
                logger.info("从配置中移除geometryTolerance以避免兼容性问题")
                settings.pop('geometryTolerance')
            
            for key, value in settings.items():
                extractor.settings[key] = value
            
            return extractor
            
        except Exception as e:
            logger.error(f"设置特征提取器失败: {str(e)}")
            # 尝试使用更简单的配置
            logger.info("尝试使用最小化配置创建特征提取器")
            extractor = featureextractor.RadiomicsFeatureExtractor()
            return extractor
    
    def load_image_and_mask(self, image_path: str, mask_path: str) -> Tuple[sitk.Image, sitk.Image]:
        """
        加载图像和掩码
        
        Args:
            image_path: 图像文件路径
            mask_path: 掩码文件路径
            
        Returns:
            Tuple[sitk.Image, sitk.Image]: 图像和掩码的SimpleITK对象
        """
        # 加载图像
        if image_path.endswith('.nii') or image_path.endswith('.nii.gz'):
            image = sitk.ReadImage(image_path)
        else:
            # 如果是其他格式，先用nibabel加载再转换
            nib_img = nib.load(image_path)
            image_array = nib_img.get_fdata()
            image = sitk.GetImageFromArray(image_array)
            
            # 设置spacing和origin
            if hasattr(nib_img.header, 'get_zooms'):
                spacing = nib_img.header.get_zooms()[:3]
                image.SetSpacing(spacing)
        
        # 加载掩码
        if mask_path.endswith('.nii') or mask_path.endswith('.nii.gz'):
            mask = sitk.ReadImage(mask_path)
        else:
            nib_mask = nib.load(mask_path)
            mask_array = nib_mask.get_fdata().astype(np.uint8)
            mask = sitk.GetImageFromArray(mask_array)
            
            # 确保掩码和图像有相同的spacing
            mask.SetSpacing(image.GetSpacing())
            mask.SetOrigin(image.GetOrigin())
            mask.SetDirection(image.GetDirection())
        
        return image, mask
    
    def validate_mask(self, mask: sitk.Image, nodule_id: str = None) -> bool:
        """
        验证掩码质量
        
        Args:
            mask: 掩码图像
            nodule_id: 结节ID，用于详细日志
            
        Returns:
            bool: 掩码是否有效
        """
        try:
            mask_array = sitk.GetArrayFromImage(mask)
            
            # 检查掩码数组是否有效
            if mask_array is None or mask_array.size == 0:
                logger.warning(f"结节 {nodule_id}: 掩码数组无效或为空")
                return False
            
            # 检查掩码是否为空
            total_voxels = np.sum(mask_array > 0)
            if total_voxels == 0:
                logger.warning(f"结节 {nodule_id}: 掩码为空，无有效体素")
                return False
            
            # 动态调整最小体素要求
            min_voxels = self.config['processing']['min_voxel_count']
            
            # 对于非常小的结节，降低要求
            if total_voxels < min_voxels:
                if total_voxels >= 8:  # 至少2x2x2体素
                    logger.info(f"结节 {nodule_id}: 体素数量 ({total_voxels}) 少于标准要求 ({min_voxels})，但满足最低要求，继续处理")
                    return True
                else:
                    logger.warning(f"结节 {nodule_id}: 体素数量 ({total_voxels}) 过少，无法进行可靠的特征提取")
                    return False
            
            # 检查掩码连通性
            if self.config.get('advanced', {}).get('validation', {}).get('check_mask_connectivity', False):
                from scipy import ndimage
                labeled_array, num_features = ndimage.label(mask_array > 0)
                if num_features > 1:
                    logger.warning(f"结节 {nodule_id}: 掩码包含 {num_features} 个不连通区域")
                    # 选择最大的连通区域
                    largest_component = np.argmax(np.bincount(labeled_array.flat)[1:]) + 1
                    mask_array = (labeled_array == largest_component).astype(mask_array.dtype)
                    logger.info(f"结节 {nodule_id}: 已选择最大连通区域进行特征提取")
            
            logger.info(f"结节 {nodule_id}: 掩码验证通过，体素数量: {total_voxels}")
            return True
            
        except Exception as e:
            logger.error(f"结节 {nodule_id}: 掩码验证过程中发生错误: {str(e)}")
            return False
    
    def _ensure_spatial_consistency(self, image: sitk.Image, mask: sitk.Image) -> Tuple[sitk.Image, sitk.Image]:
        """
        确保图像和掩码的空间信息一致
        
        Args:
            image: 原始图像
            mask: 分割掩码
            
        Returns:
            Tuple[sitk.Image, sitk.Image]: 空间信息一致的图像和掩码
        """
        try:
            # 检查尺寸是否一致
            if image.GetSize() != mask.GetSize():
                logger.warning("图像和掩码尺寸不一致，尝试重采样掩码")
                # 重采样掩码到图像尺寸
                resampler = sitk.ResampleImageFilter()
                resampler.SetReferenceImage(image)
                resampler.SetInterpolator(sitk.sitkNearestNeighbor)
                mask = resampler.Execute(mask)
            
            # 检查spacing是否一致（使用容差比较）
            image_spacing = image.GetSpacing()
            mask_spacing = mask.GetSpacing()
            spacing_tolerance = 1e-6
            
            spacing_diff = [abs(a - b) for a, b in zip(image_spacing, mask_spacing)]
            if any(diff > spacing_tolerance for diff in spacing_diff):
                logger.info(f"调整掩码spacing从 {mask_spacing} 到 {image_spacing}")
                mask.SetSpacing(image.GetSpacing())
            
            # 检查origin是否一致（使用容差比较）
            image_origin = image.GetOrigin()
            mask_origin = mask.GetOrigin()
            origin_tolerance = 1e-6
            
            origin_diff = [abs(a - b) for a, b in zip(image_origin, mask_origin)]
            if any(diff > origin_tolerance for diff in origin_diff):
                logger.info(f"调整掩码origin从 {mask_origin} 到 {image_origin}")
                mask.SetOrigin(image.GetOrigin())
            
            # 检查direction是否一致（使用容差比较）
            image_direction = image.GetDirection()
            mask_direction = mask.GetDirection()
            direction_tolerance = 1e-6
            
            direction_diff = [abs(a - b) for a, b in zip(image_direction, mask_direction)]
            if any(diff > direction_tolerance for diff in direction_diff):
                logger.info(f"调整掩码direction矩阵")
                mask.SetDirection(image.GetDirection())
            
            return image, mask
            
        except Exception as e:
            raise RuntimeError(f"空间信息一致性检查失败: {str(e)}")
    
    def extract_features(self, image: sitk.Image, mask: sitk.Image, 
                        nodule_id: str = None) -> Dict[str, Any]:
        """
        提取影像组学特征
        
        Args:
            image: 原始图像
            mask: 分割掩码
            nodule_id: 结节ID
            
        Returns:
            Dict[str, Any]: 提取的特征字典
        """
        try:
            # 验证输入
            if image is None or mask is None:
                logger.error(f"结节 {nodule_id}: 输入图像或掩码为空")
                return {}
            
            # 验证掩码
            if self.config['processing']['quality_check']:
                if not self.validate_mask(mask, nodule_id):
                    logger.warning(f"结节 {nodule_id}: 掩码验证失败，跳过特征提取")
                    return {}
            
            # 确保图像和掩码的空间信息一致
            try:
                image, mask = self._ensure_spatial_consistency(image, mask)
            except Exception as e:
                logger.error(f"结节 {nodule_id}: 空间信息一致性检查失败: {str(e)}")
                return {}
            
            # 提取特征
            logger.info(f"结节 {nodule_id}: 开始提取影像组学特征")
            
            # 设置超时机制
            import signal
            
            # 超时处理函数改为使用threading.Timer
            import threading
            timer = None
            
            try:
                # 创建超时定时器
                def raise_timeout():
                    raise TimeoutError("特征提取超时")
                
                timeout_seconds = self.config.get('performance', {}).get('computation_optimization', {}).get('feature_timeout', 300)
                timer = threading.Timer(timeout_seconds, raise_timeout)
                timer.start()
                
                # 执行特征提取
                try:
                    # 直接尝试提取特征
                    features = self.extractor.execute(image, mask)
                except Exception as feature_error:
                    error_msg = str(feature_error)
                    
                    # 检查是否是坐标容差错误
                    if "CoordinateTolerance" in error_msg or "DirectionTolerance" in error_msg or "SetGlobalDefault" in error_msg:
                        logger.warning("检测到坐标容差错误，使用直接计算方式...")
                        
                        # 使用PyRadiomics直接计算方式
                        try:
                            # 创建新的特征提取器，最小化配置
                            minimal_extractor = featureextractor.RadiomicsFeatureExtractor()
                            minimal_extractor.disableAllFeatures()
                            minimal_extractor.enableFeatureClassByName('firstorder')
                            minimal_extractor.enableFeatureClassByName('shape')
                            minimal_extractor.enableImageTypeByName('Original')
                            
                            # 跳过容差设置
                            minimal_extractor.settings.update({
                                'normalize': True,
                                'normalizeScale': 100,
                                'removeOutliers': 3
                            })
                            
                            features = minimal_extractor.execute(image, mask)
                        except Exception as minimal_error:
                            logger.error(f"最小化配置提取仍然失败: {str(minimal_error)}")
                            
                            # 最后尝试：直接从图像数据计算基本特征
                            try:
                                logger.info("尝试直接从图像数据计算基本特征")
                                features = self._compute_basic_features(image, mask, nodule_id)
                            except Exception as basic_error:
                                logger.error(f"基本特征计算失败: {str(basic_error)}")
                                features = {}
                    else:
                        # 其他类型的错误
                        logger.error(f"特征提取失败: {error_msg}")
                        features = {}
                
                # 取消定时器
                if timer:
                    timer.cancel()
                
            except TimeoutError:
                logger.error(f"结节 {nodule_id}: 特征提取超时")
                if timer:
                    timer.cancel()
                return {}
            except Exception as e:
                logger.error(f"结节 {nodule_id}: 特征提取过程中发生错误: {str(e)}")
                if timer:
                    timer.cancel()
                return {}
            
            # 验证提取的特征
            if not features or len(features) == 0:
                logger.warning(f"结节 {nodule_id}: 未提取到任何特征")
                return {}
            
            # 转换特征值类型
            processed_features = {}
            for key, value in features.items():
                if isinstance(value, np.ndarray):
                    processed_features[key] = value.tolist()
                elif isinstance(value, (np.integer, np.floating)):
                    processed_features[key] = float(value)
                else:
                    processed_features[key] = value
            
            logger.info(f"成功提取 {len(processed_features)} 个特征")
            return processed_features
            
        except Exception as e:
            logger.error(f"特征提取失败 - 结节ID: {nodule_id}, 错误: {str(e)}")
            return {}
    
    def _compute_basic_features(self, image: sitk.Image, mask: sitk.Image, nodule_id: str) -> Dict[str, Any]:
        """
        直接从图像数据计算基本特征（作为PyRadiomics提取失败时的备用选项）
        
        Args:
            image: 原始图像
            mask: 分割掩码
            nodule_id: 结节ID
            
        Returns:
            Dict[str, Any]: 基本特征字典
        """
        logger.info(f"结节 {nodule_id}: 计算基本特征")
        
        # 获取图像和掩码数据
        image_array = sitk.GetArrayFromImage(image)
        mask_array = sitk.GetArrayFromImage(mask)
        
        # 仅保留掩码区域
        masked_image = image_array * (mask_array > 0)
        voxel_values = image_array[mask_array > 0]
        
        if len(voxel_values) == 0:
            logger.warning(f"结节 {nodule_id}: 掩码内无有效体素")
            return {}
            
        # 计算基本统计特征
        features = {
            "original_firstorder_Mean": float(np.mean(voxel_values)),
            "original_firstorder_Median": float(np.median(voxel_values)),
            "original_firstorder_Minimum": float(np.min(voxel_values)),
            "original_firstorder_Maximum": float(np.max(voxel_values)),
            "original_firstorder_Range": float(np.max(voxel_values) - np.min(voxel_values)),
            "original_firstorder_StandardDeviation": float(np.std(voxel_values)),
            "original_firstorder_Variance": float(np.var(voxel_values)),
            "original_firstorder_Skewness": float(0), # 需要scipy.stats
            "original_firstorder_Kurtosis": float(0), # 需要scipy.stats
            "original_firstorder_Sum": float(np.sum(voxel_values)),
            "original_firstorder_Energy": float(np.sum(voxel_values**2)),
            "original_shape_VoxelVolume": float(np.sum(mask_array > 0)),
        }
        
        # 尝试计算更复杂的统计量
        try:
            import scipy.stats as stats
            if len(voxel_values) >= 3:  # 至少需要3个点才能计算偏度和峰度
                features["original_firstorder_Skewness"] = float(stats.skew(voxel_values))
                features["original_firstorder_Kurtosis"] = float(stats.kurtosis(voxel_values))
        except:
            pass
            
        # 尝试计算简单形状特征
        try:
            from scipy import ndimage
            
            # 计算体积相关特征
            voxel_count = np.sum(mask_array > 0)
            spacing = image.GetSpacing()
            voxel_volume_mm3 = np.prod(spacing)
            volume_mm3 = voxel_count * voxel_volume_mm3
            
            features["original_shape_VoxelCount"] = float(voxel_count)
            features["original_shape_Volume"] = float(volume_mm3)
            
            # 计算轴长度
            if voxel_count > 0:
                # 计算掩码的边界框
                nonzero = np.where(mask_array > 0)
                min_z, min_y, min_x = [np.min(arr) for arr in nonzero]
                max_z, max_y, max_x = [np.max(arr) for arr in nonzero]
                
                # 计算各轴长度（以毫米为单位）
                x_length_mm = (max_x - min_x) * spacing[0]
                y_length_mm = (max_y - min_y) * spacing[1]
                z_length_mm = (max_z - min_z) * spacing[2]
                
                features["original_shape_MajorAxisLength"] = float(max(x_length_mm, y_length_mm, z_length_mm))
                features["original_shape_MinorAxisLength"] = float(min(x_length_mm, y_length_mm, z_length_mm))
        except Exception as shape_error:
            logger.warning(f"形状特征计算失败: {str(shape_error)}")
        
        logger.info(f"结节 {nodule_id}: 已计算 {len(features)} 个基本特征")
        return features
    
    def process_single_nodule(self, nodule_data: Dict) -> Dict[str, Any]:
        """
        处理单个结节
        
        Args:
            nodule_data: 结节数据字典，包含图像路径、掩码路径等信息
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 获取文件路径
            volume_path = nodule_data.get('volume_path')
            mask_path = nodule_data.get('mask_path')
            nodule_id = nodule_data.get('nodule_id', 'unknown')
            
            if not volume_path or not mask_path:
                logger.error(f"缺少必要的文件路径 - 结节ID: {nodule_id}")
                return {}
            
            if not os.path.exists(volume_path) or not os.path.exists(mask_path):
                logger.error(f"文件不存在 - 结节ID: {nodule_id}")
                return {}
            
            # 加载图像和掩码
            image, mask = self.load_image_and_mask(volume_path, mask_path)
            
            # 提取特征
            features = self.extract_features(image, mask, nodule_id)
            
            if features:
                # 添加元数据
                result = {
                    'nodule_id': nodule_id,
                    'volume_path': volume_path,
                    'mask_path': mask_path,
                    'features': features,
                    'feature_count': len(features),
                    'extraction_success': True
                }
                
                # 添加其他元数据
                if 'metadata' in nodule_data:
                    result['metadata'] = nodule_data['metadata']
                
                return result
            else:
                return {
                    'nodule_id': nodule_id,
                    'extraction_success': False,
                    'error': '特征提取失败'
                }
                
        except Exception as e:
            logger.error(f"处理结节时出错: {str(e)}")
            return {
                'nodule_id': nodule_data.get('nodule_id', 'unknown'),
                'extraction_success': False,
                'error': str(e)
            }
    
    def load_segmentation_results(self, input_dir: str) -> List[Dict]:
        """
        加载分割结果
        
        Args:
            input_dir: 分割结果目录
            
        Returns:
            List[Dict]: 结节数据列表
        """
        nodule_data_list = []
        
        # 遍历输入目录
        input_path = Path(input_dir)
        
        for image_dir in input_path.iterdir():
            if not image_dir.is_dir():
                continue
                
            for nodule_dir in image_dir.iterdir():
                if not nodule_dir.is_dir() or not nodule_dir.name.startswith('nodule_'):
                    continue
                
                # 查找相关文件
                volume_file = None
                mask_file = None
                metadata_file = None
                
                for file in nodule_dir.iterdir():
                    if file.name.endswith('_volume.nii.gz'):
                        volume_file = str(file)
                    elif file.name.endswith('_segmentation_mask.nii.gz'):
                        mask_file = str(file)
                    elif file.name.endswith('_metadata.json'):
                        metadata_file = str(file)
                
                if volume_file and mask_file:
                    nodule_data = {
                        'nodule_id': f"{image_dir.name}_{nodule_dir.name}",
                        'volume_path': volume_file,
                        'mask_path': mask_file,
                        'image_name': image_dir.name,
                        'nodule_name': nodule_dir.name
                    }
                    
                    # 加载元数据
                    if metadata_file:
                        try:
                            with open(metadata_file, 'r') as f:
                                metadata = json.load(f)
                            nodule_data['metadata'] = metadata
                        except Exception as e:
                            logger.warning(f"无法加载元数据文件 {metadata_file}: {str(e)}")
                    
                    nodule_data_list.append(nodule_data)
                else:
                    logger.warning(f"在 {nodule_dir} 中未找到完整的文件集")
        
        logger.info(f"找到 {len(nodule_data_list)} 个结节待处理")
        return nodule_data_list
    
    def save_features(self, results: List[Dict], output_dir: str):
        """
        保存特征提取结果
        
        Args:
            results: 特征提取结果列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 过滤成功的结果
        successful_results = [r for r in results if r.get('extraction_success', False)]
        
        if not successful_results:
            logger.warning("没有成功提取的特征")
            return
        
        # 准备DataFrame
        feature_rows = []
        
        for result in successful_results:
            row = {
                'nodule_id': result['nodule_id'],
                'image_name': result.get('image_name', ''),
                'nodule_name': result.get('nodule_name', ''),
                'volume_path': result['volume_path'],
                'mask_path': result['mask_path']
            }
            
            # 添加特征
            features = result['features']
            for feature_name, feature_value in features.items():
                row[feature_name] = feature_value
            
            # 添加元数据中的统计信息
            if 'metadata' in result and 'segmentation_stats' in result['metadata']:
                stats = result['metadata']['segmentation_stats']
                for stat_name, stat_value in stats.items():
                    row[f'seg_{stat_name}'] = stat_value
            
            feature_rows.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(feature_rows)
        
        # 保存CSV
        if 'csv' in self.config['processing']['output_format']:
            csv_file = os.path.join(output_dir, 'radiomics_features.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            logger.info(f"特征CSV文件已保存至: {csv_file}")
        
        # 保存JSON
        if 'json' in self.config['processing']['output_format']:
            json_file = os.path.join(output_dir, 'radiomics_features.json')
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"特征JSON文件已保存至: {json_file}")
        
        # 保存特征描述
        feature_description = self._generate_feature_description(df)
        desc_file = os.path.join(output_dir, 'feature_description.json')
        with open(desc_file, 'w', encoding='utf-8') as f:
            json.dump(feature_description, f, indent=2, ensure_ascii=False)
        logger.info(f"特征描述文件已保存至: {desc_file}")
        
        # 生成摘要报告
        summary = self._generate_summary_report(results, df)
        summary_file = os.path.join(output_dir, 'extraction_summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        logger.info(f"摘要报告已保存至: {summary_file}")
    
    def _generate_feature_description(self, df: pd.DataFrame) -> Dict:
        """
        生成特征描述
        
        Args:
            df: 特征DataFrame
            
        Returns:
            Dict: 特征描述字典
        """
        # 获取特征列（排除元数据列）
        meta_columns = ['nodule_id', 'image_name', 'nodule_name', 'volume_path', 'mask_path']
        feature_columns = [col for col in df.columns if col not in meta_columns and not col.startswith('seg_')]
        
        description = {
            'total_features': len(feature_columns),
            'total_nodules': len(df),
            'feature_categories': {},
            'feature_statistics': {}
        }
        
        # 按类别分组特征
        categories = {}
        for col in feature_columns:
            if '_' in col:
                category = col.split('_')[1] if col.startswith('original_') else col.split('_')[0]
            else:
                category = 'other'
            
            if category not in categories:
                categories[category] = []
            categories[category].append(col)
        
        description['feature_categories'] = {k: len(v) for k, v in categories.items()}
        
        # 计算特征统计
        for col in feature_columns:
            if df[col].dtype in ['float64', 'int64']:
                description['feature_statistics'][col] = {
                    'mean': float(df[col].mean()),
                    'std': float(df[col].std()),
                    'min': float(df[col].min()),
                    'max': float(df[col].max()),
                    'missing_count': int(df[col].isna().sum())
                }
        
        return description
    
    def _generate_summary_report(self, results: List[Dict], df: pd.DataFrame) -> Dict:
        """
        生成摘要报告
        
        Args:
            results: 处理结果列表
            df: 特征DataFrame
            
        Returns:
            Dict: 摘要报告
        """
        successful_count = sum(1 for r in results if r.get('extraction_success', False))
        failed_count = len(results) - successful_count
        
        summary = {
            'processing_summary': {
                'total_nodules': len(results),
                'successful_extractions': successful_count,
                'failed_extractions': failed_count,
                'success_rate': successful_count / len(results) if results else 0
            },
            'feature_summary': {
                'total_features_extracted': len([col for col in df.columns if not col.startswith(('nodule_id', 'image_name', 'nodule_name', 'volume_path', 'mask_path', 'seg_'))]) if not df.empty else 0
            },
            'configuration': self.config,
            'failed_cases': []
        }
        
        # 记录失败案例
        for result in results:
            if not result.get('extraction_success', False):
                summary['failed_cases'].append({
                    'nodule_id': result.get('nodule_id', 'unknown'),
                    'error': result.get('error', 'unknown error')
                })
        
        return summary
    
    def process_batch(self, input_dir: str, output_dir: str):
        """
        批量处理结节特征提取
        
        Args:
            input_dir: 输入目录（分割结果）
            output_dir: 输出目录
        """
        logger.info(f"开始批量处理 - 输入目录: {input_dir}")
        logger.info(f"输出目录: {output_dir}")
        
        # 加载分割结果
        nodule_data_list = self.load_segmentation_results(input_dir)
        
        if not nodule_data_list:
            logger.error("未找到有效的分割结果")
            return
        
        # 处理每个结节
        results = []
        
        for nodule_data in tqdm(nodule_data_list, desc="提取特征"):
            result = self.process_single_nodule(nodule_data)
            results.append(result)
        
        # 保存结果
        self.save_features(results, output_dir)
        
        logger.info("批量处理完成")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="肺结节影像组学特征提取工具")
    parser.add_argument("--input-dir", type=str, required=True,
                       help="分割结果输入目录")
    parser.add_argument("--output-dir", type=str, required=True,
                       help="特征输出目录")
    parser.add_argument("--config", type=str, default=None,
                       help="配置文件路径")
    
    args = parser.parse_args()
    
    # 检查PyRadiomics是否可用
    if not RADIOMICS_AVAILABLE:
        print("错误: PyRadiomics和SimpleITK是必需的依赖")
        print("请运行: pip install pyradiomics SimpleITK")
        return
    
    try:
        # 创建特征提取器
        extractor = RadiomicsFeatureExtractor(args.config)
        
        # 批量处理
        extractor.process_batch(args.input_dir, args.output_dir)
        
        print("\n特征提取完成！")
        print(f"结果已保存至: {args.output_dir}")
        
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        raise


if __name__ == "__main__":
    main()