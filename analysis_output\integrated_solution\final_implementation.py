
# 修正后的create_triplanar_visualization函数
def create_triplanar_visualization_fixed(image_data, box, score, output_file, nodule_idx, lower_bound=None, upper_bound=None):
    """
    创建单个结节的三平面可视化，修正了所有视图中的边界框位置

    参数:
        image_data: 3D图像数据
        box: 边界框坐标 [x1, y1, z1, x2, y2, z2]，已经过flip_xy变换
        score: 检测置信度
        output_file: 输出文件路径
        nodule_idx: 结节索引
        lower_bound: 窗位下限
        upper_bound: 窗位上限
    """
    import numpy as np
    import matplotlib.pyplot as plt
    import matplotlib.gridspec as gridspec
    from matplotlib.patches import Rectangle
    from matplotlib.font_manager import FontProperties

    # 配置中文字体支持
    def setup_chinese_font():
        """配置支持中文显示的字体"""
        import sys
        try:
            # 尝试使用以下中文字体
            chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'STSong', 'NSimSun']
            
            # 检查是否有fonttools库
            try:
                from matplotlib.font_manager import fontManager
                system_fonts = [f.name for f in fontManager.ttflist]
                
                # 检查系统中是否有中文字体
                available_chinese_fonts = [font for font in chinese_fonts if font in system_fonts]
                
                if available_chinese_fonts:
                    print(f"找到可用的中文字体: {available_chinese_fonts}")
                    plt.rcParams['font.sans-serif'] = available_chinese_fonts + plt.rcParams['font.sans-serif']
                else:
                    print("未找到预设的中文字体，将尝试使用系统默认字体")
                    
                    # 在Windows系统上尝试使用微软雅黑
                    if sys.platform.startswith('win'):
                        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] + plt.rcParams['font.sans-serif']
                    # 在macOS上尝试使用苹方
                    elif sys.platform == 'darwin':
                        plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Apple LiGothic Medium'] + plt.rcParams['font.sans-serif']
                    # 在Linux上尝试使用文泉驿
                    else:
                        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei'] + plt.rcParams['font.sans-serif']
                    
            except ImportError:
                # 如果无法获取系统字体列表，则使用默认配置
                plt.rcParams['font.sans-serif'] = chinese_fonts + plt.rcParams['font.sans-serif']
                
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            plt.rcParams['font.family'] = 'sans-serif'  # 使用无衬线字体
            
            print("中文字体配置完成")
            return True
        except Exception as e:
            print(f"配置中文字体时出错: {e}")
            # 如果出错，使用回退选项
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.family'] = 'sans-serif'
            return False
    
    # 尝试获取中文字体
    def get_chinese_font():
        import os
        try:
            from matplotlib.font_manager import findfont, FontProperties
            # 尝试加载Noto Sans CJK字体，这是matplotlib中内置的支持中文的字体
            font_path = findfont(FontProperties(family=['Noto Sans CJK JP', 'Noto Sans CJK SC']))
            if os.path.exists(font_path):
                print(f"使用Matplotlib内置字体: {font_path}")
                return FontProperties(fname=font_path)
            else:
                return None
        except Exception:
            return None
    
    # 配置中文字体
    setup_chinese_font()
    chinese_font = get_chinese_font()

    print(f"\n创建结节的三平面可视化图像:")
    print(f"  - 边界框坐标: {box}")
    print(f"  - 检测置信度: {score:.4f}")

    # 计算边界框中心
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")

    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T

    # 窗位调整和归一化
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)

    # 将图像归一化为0-255范围的uint8类型
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)

    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])

    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')

    # 获取图像尺寸
    img_height_axial, img_width_axial = axial_slice_norm.shape

    # 轴状视图上的边界框 - 直接使用翻转后的坐标
    x_min_axial = max(0, box[0])
    y_min_axial = max(0, box[1])
    width_axial = min(img_width_axial-1, box[3]) - x_min_axial
    height_axial = min(img_height_axial-1, box[4]) - y_min_axial

    rect_axial = Rectangle(
        (x_min_axial, y_min_axial), width_axial, height_axial,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax1.add_patch(rect_axial)
    
    # 根据是否有中文字体设置标题
    if chinese_font:
        ax1.set_title(f'轴状视图 (z={z_center})', fontproperties=chinese_font)
    else:
        ax1.set_title(f'Axial View (z={z_center})')
    
    ax1.axis('off')

    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')

    # 冠状视图上的边界框 - 交换X和Z的映射
    img_height_coronal, img_width_coronal = coronal_slice_norm.shape

    # 冠状视图中，交换X和Z的映射：X为横坐标，Z为纵坐标
    x_min_coronal = max(0, box[0])
    y_min_coronal = max(0, box[2])
    width_coronal = min(img_width_coronal-1, box[3]) - x_min_coronal
    height_coronal = min(img_height_coronal-1, box[5]) - y_min_coronal

    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    
    # 根据是否有中文字体设置标题
    if chinese_font:
        ax2.set_title(f'冠状视图 (y={y_center})', fontproperties=chinese_font)
    else:
        ax2.set_title(f'Coronal View (y={y_center})')
    
    ax2.axis('off')

    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')

    # 矢状视图上的边界框 - 交换Y和Z的映射
    img_height_sagittal, img_width_sagittal = sagittal_slice_norm.shape

    # 矢状视图中，交换Y和Z的映射：Y为横坐标，Z为纵坐标
    x_min_sagittal = max(0, box[1])
    y_min_sagittal = max(0, box[2])
    width_sagittal = min(img_width_sagittal-1, box[4]) - x_min_sagittal
    height_sagittal = min(img_height_sagittal-1, box[5]) - y_min_sagittal

    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
        linewidth=2, edgecolor='r', facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    
    # 根据是否有中文字体设置标题
    if chinese_font:
        ax3.set_title(f'矢状视图 (x={x_center})', fontproperties=chinese_font)
    else:
        ax3.set_title(f'Sagittal View (x={x_center})')
    
    ax3.axis('off')

    # 添加置信度信息
    if chinese_font:
        plt.suptitle(f'结节 #{nodule_idx+1} - 置信度: {score:.4f}', fontsize=16, fontproperties=chinese_font)
    else:
        plt.suptitle(f'Nodule #{nodule_idx+1} - Score: {score:.4f}', fontsize=16)

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"  - 三平面可视化图像已保存至: {output_file}")
    return True
