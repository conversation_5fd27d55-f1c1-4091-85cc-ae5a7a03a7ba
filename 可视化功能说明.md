# 肺结节检测可视化功能说明

## 概述

肺结节检测系统的可视化功能已经完全集成到GUI界面中，用户可以通过图形界面轻松生成和查看检测结果的可视化图像。

## 功能特点

### 1. 集成的可视化界面
- **位置**: GUI主界面 → "后处理" 选项卡 → "结果可视化" 区域
- **功能**: 提供完整的可视化参数配置和执行控制

### 2. 可配置的可视化参数
- **置信度阈值**: 控制显示哪些检测结果（默认0.3）
- **最大结节数**: 限制生成的可视化图像数量（默认10个）
- **输出目录**: 自定义可视化图像保存位置

### 3. 多种操作方式
- **生成可视化**: 根据检测结果生成三平面可视化图像
- **预览可视化**: 查看已生成的可视化文件列表
- **打开可视化目录**: 直接打开输出目录查看结果

## 使用方法

### 方法一：从后处理选项卡生成可视化

1. **运行检测任务**
   - 在"结节检测"选项卡中完成检测任务
   - 确保生成了检测结果文件

2. **配置可视化参数**
   - 切换到"后处理"选项卡
   - 在"结果可视化"区域设置：
     - 可视化输出目录
     - 置信度阈值（推荐0.3-0.5）
     - 最大结节数（推荐5-10个）

3. **生成可视化**
   - 点击"生成可视化"按钮
   - 系统将自动调用可视化脚本
   - 在处理日志中查看进度

4. **查看结果**
   - 点击"打开可视化目录"查看生成的PNG图像
   - 或点击"预览可视化"查看文件列表

### 方法二：从结果查看选项卡生成可视化

1. **加载检测结果**
   - 切换到"结果查看"选项卡
   - 浏览并选择检测结果JSON文件
   - 点击"加载结果"查看统计信息

2. **生成可视化**
   - 点击"生成可视化"按钮
   - 系统将使用默认参数生成可视化

3. **预览结果**
   - 点击"预览可视化"查看生成的文件
   - 点击"打开输出目录"直接查看文件

## 可视化图像说明

### 图像内容
每个检测到的结节会生成一个PNG文件，包含：
- **三平面视图**: 轴状面、冠状面、矢状面
- **结节标注**: 红色矩形框标记结节位置
- **信息显示**: 
  - 结节编号
  - 检测置信度
  - 坐标信息
  - 窗位设置（肺窗：-1000到100 HU）

### 文件命名
- 格式：`nodule_X_optimized.png`
- X为结节编号（按置信度从高到低排序）

## 技术实现

### 调用的脚本
- **主脚本**: `visualize_nodules_optimized.py`
- **功能**: 优化版的三平面可视化生成器
- **特点**: 支持坐标变换、中文字体、调试模式

### 命令行参数
```bash
python visualize_nodules_optimized.py \
    --predictions <检测结果文件> \
    --output <输出目录> \
    --threshold <置信度阈值> \
    --max-nodules <最大结节数> \
    --base-dir <图像基础目录>
```

### 路径解析逻辑
1. **检查绝对路径**: 如果JSON中的路径是绝对路径且文件存在，直接使用
2. **基础目录组合**: 将相对路径与基础目录组合，检查文件是否存在
3. **当前目录查找**: 在当前工作目录中查找文件
4. **文件名匹配**: 仅使用文件名在基础目录中查找
5. **错误报告**: 如果所有尝试都失败，提供详细的错误信息

### 错误处理
- 自动检查检测结果文件是否存在
- 自动创建输出目录
- 实时显示处理进度和错误信息
- 统计生成的可视化文件数量

## 常见问题

### Q1: 点击"生成可视化"后没有反应？
**A**: 检查以下几点：
- 确保已运行检测任务并生成结果文件
- 检查检测结果文件路径是否正确
- 查看处理日志中的错误信息

### Q2: 生成的可视化图像为空或显示异常？
**A**: 可能的原因：
- 图像文件路径不正确
- 图像文件格式不支持
- 坐标变换参数需要调整

### Q3: 如何调整可视化参数？
**A**: 在"后处理"选项卡的"结果可视化"区域：
- 降低置信度阈值可显示更多结节
- 增加最大结节数可生成更多图像
- 修改输出目录可改变保存位置

### Q4: 提示"无法找到图像文件"怎么办？
**A**: 这通常是路径问题，请检查：
- 确保在"结节检测"选项卡中正确设置了"图像数据目录"
- 检查JSON文件中的图像路径是否正确
- 如果使用相对路径，确保相对于设置的基础目录
- 查看处理日志中的详细路径解析信息

### Q5: 如何处理不同的图像路径格式？
**A**: 系统支持多种路径格式：
- **绝对路径**: 如 `D:\data\image.nii.gz`，直接使用
- **相对路径**: 如 `subfolder\image.nii.gz`，相对于基础目录
- **文件名**: 如 `image.nii.gz`，在基础目录中查找
- 系统会自动尝试所有可能的路径组合

### Q6: 可视化图像质量如何优化？
**A**: 系统已使用优化版可视化脚本：
- 自动窗位调整（肺窗设置）
- 高质量图像输出
- 中文字体支持
- 三平面同步显示

## 路径处理功能

### 智能路径解析
系统现在支持智能路径解析，能够自动处理检测结果JSON文件中的图像路径：

1. **绝对路径**: 如果JSON中包含绝对路径且文件存在，直接使用
2. **相对路径**: 结合基础目录（data_base_dir）解析相对路径
3. **文件名匹配**: 如果只有文件名，在基础目录中查找匹配文件
4. **错误处理**: 提供详细的路径解析错误信息

### 基础目录配置
- **GUI设置**: 在"结节检测"选项卡中设置"图像数据目录"
- **自动传递**: 可视化功能自动使用该目录作为基础路径
- **路径解析**: 支持相对路径到绝对路径的自动转换

## 更新日志

### 2025-01-31 (最新更新)
- ✅ **重大改进**: 添加智能图像路径解析功能
- ✅ **新功能**: 支持基础目录参数，自动处理相对路径
- ✅ **增强**: 改进路径错误处理和调试信息
- ✅ **集成**: GUI自动传递data_base_dir作为基础目录
- ✅ **兼容**: 保持对现有绝对路径的完全兼容

### 2025-01-31 (早期更新)
- ✅ 修复GUI中可视化功能调用错误
- ✅ 添加可视化参数配置界面
- ✅ 实现可视化结果预览功能
- ✅ 添加输出目录快速打开功能
- ✅ 改进错误处理和用户反馈
- ✅ 完善日志显示和进度监控

## 总结

肺结节检测系统的可视化功能现已完全集成到GUI界面中，提供了：
- 直观的参数配置界面
- 便捷的一键生成功能
- 完善的结果预览和管理
- 详细的进度反馈和错误处理

用户可以通过简单的点击操作，快速生成高质量的结节检测可视化图像，大大提升了系统的易用性和实用性。
