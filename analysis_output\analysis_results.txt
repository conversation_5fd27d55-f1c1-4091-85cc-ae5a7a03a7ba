医学图像坐标系分析报告
生成时间: 当前日期: 2025/05/30 周五 
输入新日期: (年月日)
脚本版本: 1.0

================================================================================

================================================================================
  预测结果文件分析
================================================================================
JSON文件: ct_detection/output/results.json
顶级键: ['validation', 'coordinate_system_info']
验证集预测数量: 1

第一个预测结果的键: ['label', 'box', 'score', 'image']
图像路径: D:\AIlung_test\CYJ\cyj_lps_segmented_cropped_lung.nii.gz
边界框数量: 34
第一个边界框: [138.8970947265625, 230.28981018066406, 62.05156326293945, 154.802001953125, 245.82228088378906, 70.8878173828125]
边界框格式: [x1, y1, z1, x2, y2, z2]
边界框尺寸统计:
  宽度: 最小=3.6, 最大=25.5, 平均=7.6
  高度: 最小=3.3, 最大=26.2, 平均=7.6
  深度: 最小=2.5, 最大=14.5, 平均=5.1
分数数量: 34
分数范围: 0.0201 - 0.9986

坐标系统信息:
  description: 包含坐标变换信息，用于调试边界框问题
  affine_lps_to_ras: True
  gt_box_mode: cccwhd

================================================================================
  坐标转换分析
================================================================================
测试图像形状: (512, 512, 128)

测试边界框:

边界框 #1: [100, 100, 50, 150, 150, 70]
尺寸: 宽=50, 高=50, 深=20
中心点: (125.0, 125.0, 60.0)
调整坐标 - 原始边界框: [100.0, 100.0, 50.0, 150.0, 150.0, 70.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: 0
调整坐标 - 边界框调整后: [100.0, 100.0, 50.0, 150.0, 150.0, 70.0]
调整坐标 - 调整后尺寸: 宽=50.0, 高=50.0, 深=20.0
调整坐标 - 调整后中心点: (125.0, 125.0, 60.0)

z_offset=0时的调整结果:
调整后边界框: [100, 100, 50, 150, 150, 70]
调整后尺寸: 宽=50, 高=50, 深=20
调整后中心点: (125.0, 125.0, 60.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=0.0
调整坐标 - 原始边界框: [100.0, 100.0, 50.0, 150.0, 150.0, 70.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: 10
调整坐标 - 边界框调整后: [100.0, 100.0, 60.0, 150.0, 150.0, 80.0]
调整坐标 - 调整后尺寸: 宽=50.0, 高=50.0, 深=20.0
调整坐标 - 调整后中心点: (125.0, 125.0, 70.0)

z_offset=10时的调整结果:
调整后边界框: [100, 100, 60, 150, 150, 80]
调整后尺寸: 宽=50, 高=50, 深=20
调整后中心点: (125.0, 125.0, 70.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=10.0
调整坐标 - 原始边界框: [100.0, 100.0, 50.0, 150.0, 150.0, 70.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: -10
调整坐标 - 边界框调整后: [100.0, 100.0, 40.0, 150.0, 150.0, 60.0]
调整坐标 - 调整后尺寸: 宽=50.0, 高=50.0, 深=20.0
调整坐标 - 调整后中心点: (125.0, 125.0, 50.0)

z_offset=-10时的调整结果:
调整后边界框: [100, 100, 40, 150, 150, 60]
调整后尺寸: 宽=50, 高=50, 深=20
调整后中心点: (125.0, 125.0, 50.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=-10.0

边界框 #2: [10, 10, 10, 30, 30, 20]
尺寸: 宽=20, 高=20, 深=10
中心点: (20.0, 20.0, 15.0)
调整坐标 - 原始边界框: [10.0, 10.0, 10.0, 30.0, 30.0, 20.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: 0
调整坐标 - 边界框调整后: [10.0, 10.0, 10.0, 30.0, 30.0, 20.0]
调整坐标 - 调整后尺寸: 宽=20.0, 高=20.0, 深=10.0
调整坐标 - 调整后中心点: (20.0, 20.0, 15.0)

z_offset=0时的调整结果:
调整后边界框: [10, 10, 10, 30, 30, 20]
调整后尺寸: 宽=20, 高=20, 深=10
调整后中心点: (20.0, 20.0, 15.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=0.0
调整坐标 - 原始边界框: [10.0, 10.0, 10.0, 30.0, 30.0, 20.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: 10
调整坐标 - 边界框调整后: [10.0, 10.0, 20.0, 30.0, 30.0, 30.0]
调整坐标 - 调整后尺寸: 宽=20.0, 高=20.0, 深=10.0
调整坐标 - 调整后中心点: (20.0, 20.0, 25.0)

z_offset=10时的调整结果:
调整后边界框: [10, 10, 20, 30, 30, 30]
调整后尺寸: 宽=20, 高=20, 深=10
调整后中心点: (20.0, 20.0, 25.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=10.0
调整坐标 - 原始边界框: [10.0, 10.0, 10.0, 30.0, 30.0, 20.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: -10
调整坐标 - 边界框调整后: [10.0, 10.0, 0.0, 30.0, 30.0, 10.0]
调整坐标 - 调整后尺寸: 宽=20.0, 高=20.0, 深=10.0
调整坐标 - 调整后中心点: (20.0, 20.0, 5.0)

z_offset=-10时的调整结果:
调整后边界框: [10, 10, 0, 30, 30, 10]
调整后尺寸: 宽=20, 高=20, 深=10
调整后中心点: (20.0, 20.0, 5.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=-10.0

边界框 #3: [450, 450, 100, 490, 490, 120]
尺寸: 宽=40, 高=40, 深=20
中心点: (470.0, 470.0, 110.0)
调整坐标 - 原始边界框: [450.0, 450.0, 100.0, 490.0, 490.0, 120.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: 0
调整坐标 - 边界框调整后: [450.0, 450.0, 100.0, 490.0, 490.0, 120.0]
调整坐标 - 调整后尺寸: 宽=40.0, 高=40.0, 深=20.0
调整坐标 - 调整后中心点: (470.0, 470.0, 110.0)

z_offset=0时的调整结果:
调整后边界框: [450, 450, 100, 490, 490, 120]
调整后尺寸: 宽=40, 高=40, 深=20
调整后中心点: (470.0, 470.0, 110.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=0.0
调整坐标 - 原始边界框: [450.0, 450.0, 100.0, 490.0, 490.0, 120.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: 10
调整坐标 - 边界框调整后: [450.0, 450.0, 110.0, 490.0, 490.0, 127.0]
调整坐标 - 调整后尺寸: 宽=40.0, 高=40.0, 深=17.0
调整坐标 - 调整后中心点: (470.0, 470.0, 118.5)

z_offset=10时的调整结果:
调整后边界框: [450, 450, 110, 490, 490, 127]
调整后尺寸: 宽=40, 高=40, 深=17
调整后中心点: (470.0, 470.0, 118.5)
中心点变化: x差异=0.0, y差异=0.0, z差异=8.5
调整坐标 - 原始边界框: [450.0, 450.0, 100.0, 490.0, 490.0, 120.0]
调整坐标 - 图像形状: (512, 512, 128), Z轴偏移量: -10
调整坐标 - 边界框调整后: [450.0, 450.0, 90.0, 490.0, 490.0, 110.0]
调整坐标 - 调整后尺寸: 宽=40.0, 高=40.0, 深=20.0
调整坐标 - 调整后中心点: (470.0, 470.0, 100.0)

z_offset=-10时的调整结果:
调整后边界框: [450, 450, 90, 490, 490, 110]
调整后尺寸: 宽=40, 高=40, 深=20
调整后中心点: (470.0, 470.0, 100.0)
中心点变化: x差异=0.0, y差异=0.0, z差异=-10.0

模拟坐标系转换 (LPS <-> RAS):
原始点 (RAS): [100, 200, 50]
转换到LPS: [-100, -200, 50]
转回RAS: [100, 200, 50]

================================================================================
  边界框绘制逻辑分析
================================================================================
测试数据形状: (100, 100, 50)
对象中心: (50, 50, 25)
测试边界框: [38, 38, 19, 62, 62, 31]
轴状切片形状: (100, 100)
冠状切片形状: (50, 100)
矢状切片形状: (50, 100)

Matplotlib绘图坐标系分析:
imshow默认原点: 左上角 (0,0)
x坐标从左到右增加
y坐标从上到下增加

轴状视图 (Axial View) 坐标映射:
显示的是x-y平面上的切片
由于转置操作(.T)，图像的行列已交换:
  原始数据坐标 (x, y, z) -> 图像坐标 (y, x)

冠状视图 (Coronal View) 坐标映射:
显示的是x-z平面上的切片
由于转置操作(.T)，图像的行列已交换:
  原始数据坐标 (x, y, z) -> 图像坐标 (z, x)

矢状视图 (Sagittal View) 坐标映射:
显示的是y-z平面上的切片
由于转置操作(.T)，图像的行列已交换:
  原始数据坐标 (x, y, z) -> 图像坐标 (z, y)

在轴状视图上绘制边界框的正确方式:
边界框原始坐标: [x1=38, y1=38, z1=19, x2=62, y2=62, z2=31]

当前绘制方法分析:
- 轴状视图: 使用(y1, x1)作为左上角坐标，宽度为(y2-y1)，高度为(x2-x1)
- 冠状视图: 使用(z1, x1)作为左上角坐标，宽度为(z2-z1)，高度为(x2-x1)
- 矢状视图: 使用(z1, y1)作为左上角坐标，宽度为(z2-z1)，高度为(y2-y1)

绘制测试图像已保存至: analysis_output/drawing_test.png
