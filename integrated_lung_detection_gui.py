#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肺结节检测系统集成GUI界面
集成检测、分割、分类、影像组学等完整功能

主要功能：
1. 检测任务配置和运行
2. 结节分割功能
3. 良恶性分类
4. 影像组学特征提取
5. 结果可视化
6. 批量处理管理

Author: AI Assistant
Date: 2025-01-31
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import sys
import threading
import subprocess
from pathlib import Path
import queue
from datetime import datetime

# 尝试导入torch，如果失败则使用默认值
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

class IntegratedLungDetectionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("肺结节检测系统 - 集成界面")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 配置变量
        self.config_vars = {
            # 检测配置
            'model_path': tk.StringVar(),
            'data_base_dir': tk.StringVar(),
            'result_list_file_path': tk.StringVar(),
            'score_thresh': tk.DoubleVar(value=0.02),
            'nms_thresh': tk.DoubleVar(value=0.22),
            'batch_size': tk.IntVar(value=1),
            'patch_size_x': tk.IntVar(value=192),
            'patch_size_y': tk.IntVar(value=192),
            'patch_size_z': tk.IntVar(value=80),
            'val_patch_size_x': tk.IntVar(value=512),
            'val_patch_size_y': tk.IntVar(value=512),
            'val_patch_size_z': tk.IntVar(value=208),
            
            # 数据选择方式
            'data_selection_mode': tk.StringVar(value='files'),  # 'files' 或 'json'
            'selected_files': [],  # 存储选择的文件列表
            
            # 其他功能配置
            'segmentation_output_dir': tk.StringVar(value='output/segmentation_3d_fixed'),
            'classification_model': tk.StringVar(value='SWS++'),
            'radiomics_output_dir': tk.StringVar(value='output/radiomics_features'),
            'visualization_output_dir': tk.StringVar(value='output/visualizations')
        }
        
        # 日志队列
        self.log_queue = queue.Queue()
        
        # 当前运行的任务
        self.current_task = None
        
        self.create_widgets()
        self.load_existing_config()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="肺结节检测系统 - 集成界面", 
                               font=('Arial', 18, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建各个选项卡
        self.create_detection_tab()
        self.create_processing_tab()
        self.create_batch_tab()
        self.create_results_tab()
        self.create_settings_tab()
        
    def create_detection_tab(self):
        """创建检测配置选项卡"""
        detection_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(detection_frame, text="结节检测")
        
        # 配置网格权重
        detection_frame.columnconfigure(0, weight=1)
        detection_frame.rowconfigure(3, weight=1)
        
        # 基本配置区域
        self.create_basic_config_section(detection_frame, 0)
        
        # 数据选择区域
        self.create_data_selection_section(detection_frame, 1)
        
        # 检测参数区域
        self.create_detection_params_section(detection_frame, 2)
        
        # 日志和控制区域
        self.create_log_and_control_section(detection_frame, 3)
        
    def create_basic_config_section(self, parent, row):
        """创建基本配置区域"""
        config_frame = ttk.LabelFrame(parent, text="基本配置", padding="10")
        config_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 预训练模型路径
        ttk.Label(config_frame, text="预训练模型:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(config_frame, textvariable=self.config_vars['model_path'], width=60).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(config_frame, text="浏览", 
                  command=lambda: self.browse_file('model_path', 'PyTorch模型文件', '*.pt')).grid(
            row=0, column=2, padx=(5, 0), pady=2)
        
        # 图像数据根目录
        ttk.Label(config_frame, text="图像数据目录:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(config_frame, textvariable=self.config_vars['data_base_dir'], width=60).grid(
            row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(config_frame, text="浏览", 
                  command=lambda: self.browse_directory('data_base_dir')).grid(
            row=1, column=2, padx=(5, 0), pady=2)
        
        # 检测结果输出路径
        ttk.Label(config_frame, text="结果输出路径:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(config_frame, textvariable=self.config_vars['result_list_file_path'], width=60).grid(
            row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(config_frame, text="浏览", 
                  command=lambda: self.browse_save_file('result_list_file_path', 'JSON文件', '*.json')).grid(
            row=2, column=2, padx=(5, 0), pady=2)
        
        # 快速设置按钮
        quick_frame = ttk.Frame(config_frame)
        quick_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(quick_frame, text="设置默认路径", 
                  command=self.set_default_paths).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_frame, text="验证配置", 
                  command=self.validate_detection_config).pack(side=tk.LEFT, padx=5)
        
    def create_data_selection_section(self, parent, row):
        """创建数据选择区域"""
        data_frame = ttk.LabelFrame(parent, text="数据选择", padding="10")
        data_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        data_frame.columnconfigure(1, weight=1)
        
        # 选择方式
        mode_frame = ttk.Frame(data_frame)
        mode_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(mode_frame, text="数据选择方式:").pack(side=tk.LEFT)
        
        ttk.Radiobutton(mode_frame, text="直接选择文件", 
                       variable=self.config_vars['data_selection_mode'], 
                       value='files',
                       command=self.on_data_mode_change).pack(side=tk.LEFT, padx=(10, 5))
        
        ttk.Radiobutton(mode_frame, text="加载JSON列表", 
                       variable=self.config_vars['data_selection_mode'], 
                       value='json',
                       command=self.on_data_mode_change).pack(side=tk.LEFT, padx=5)
        
        # 文件选择区域
        self.files_frame = ttk.Frame(data_frame)
        self.files_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        self.files_frame.columnconfigure(0, weight=1)
        
        # 文件列表
        list_frame = ttk.Frame(self.files_frame)
        list_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E))
        list_frame.columnconfigure(0, weight=1)
        
        ttk.Label(list_frame, text="选择的图像文件:").grid(row=0, column=0, sticky=tk.W)
        
        # 创建文件列表框
        self.files_listbox = tk.Listbox(list_frame, height=4, selectmode=tk.EXTENDED)
        self.files_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 滚动条
        files_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        files_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.files_listbox.config(yscrollcommand=files_scrollbar.set)
        
        # 文件操作按钮
        files_button_frame = ttk.Frame(self.files_frame)
        files_button_frame.grid(row=1, column=0, columnspan=3, pady=(5, 0))
        
        ttk.Button(files_button_frame, text="添加文件", 
                  command=self.add_image_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(files_button_frame, text="添加目录", 
                  command=self.add_image_directory).pack(side=tk.LEFT, padx=5)
        ttk.Button(files_button_frame, text="移除选中", 
                  command=self.remove_selected_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(files_button_frame, text="清空列表", 
                  command=self.clear_file_list).pack(side=tk.LEFT, padx=5)
        
        # JSON文件选择区域
        self.json_frame = ttk.Frame(data_frame)
        self.json_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))
        self.json_frame.columnconfigure(1, weight=1)
        
        ttk.Label(self.json_frame, text="JSON数据列表:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.json_entry = ttk.Entry(self.json_frame, width=60)
        self.json_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(self.json_frame, text="浏览", 
                  command=self.browse_json_file).grid(row=0, column=2, padx=(5, 0), pady=2)
        
        # 初始状态设置
        self.on_data_mode_change()
        
    def create_detection_params_section(self, parent, row):
        """创建检测参数配置区域"""
        params_frame = ttk.LabelFrame(parent, text="检测参数", padding="10")
        params_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 参数网格布局
        # 第一行：阈值参数
        ttk.Label(params_frame, text="置信度阈值:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5), pady=2)
        ttk.Entry(params_frame, textvariable=self.config_vars['score_thresh'], width=10).grid(
            row=0, column=1, padx=5, pady=2)
        
        ttk.Label(params_frame, text="NMS阈值:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=2)
        ttk.Entry(params_frame, textvariable=self.config_vars['nms_thresh'], width=10).grid(
            row=0, column=3, padx=5, pady=2)
        
        ttk.Label(params_frame, text="批处理大小:").grid(row=0, column=4, sticky=tk.W, padx=(20, 5), pady=2)
        ttk.Entry(params_frame, textvariable=self.config_vars['batch_size'], width=10).grid(
            row=0, column=5, padx=5, pady=2)
        
        # 第二行：训练块大小
        ttk.Label(params_frame, text="训练块大小:").grid(row=1, column=0, sticky=tk.W, pady=(10, 2))
        
        patch_frame = ttk.Frame(params_frame)
        patch_frame.grid(row=1, column=1, columnspan=2, sticky=tk.W, pady=(10, 2))
        
        ttk.Entry(patch_frame, textvariable=self.config_vars['patch_size_x'], width=6).pack(side=tk.LEFT)
        ttk.Label(patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(patch_frame, textvariable=self.config_vars['patch_size_y'], width=6).pack(side=tk.LEFT)
        ttk.Label(patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(patch_frame, textvariable=self.config_vars['patch_size_z'], width=6).pack(side=tk.LEFT)
        
        # 第三行：验证块大小
        ttk.Label(params_frame, text="验证块大小:").grid(row=2, column=0, sticky=tk.W, pady=2)
        
        val_patch_frame = ttk.Frame(params_frame)
        val_patch_frame.grid(row=2, column=1, columnspan=2, sticky=tk.W, pady=2)
        
        ttk.Entry(val_patch_frame, textvariable=self.config_vars['val_patch_size_x'], width=6).pack(side=tk.LEFT)
        ttk.Label(val_patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(val_patch_frame, textvariable=self.config_vars['val_patch_size_y'], width=6).pack(side=tk.LEFT)
        ttk.Label(val_patch_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(val_patch_frame, textvariable=self.config_vars['val_patch_size_z'], width=6).pack(side=tk.LEFT)
        
        # 预设参数按钮
        preset_frame = ttk.Frame(params_frame)
        preset_frame.grid(row=1, column=3, columnspan=3, rowspan=2, sticky=(tk.W, tk.N), padx=(20, 0))
        
        ttk.Label(preset_frame, text="预设参数:", font=('Arial', 9, 'bold')).pack(anchor=tk.W)
        ttk.Button(preset_frame, text="高敏感度", width=12,
                  command=lambda: self.set_preset_params('high_sensitivity')).pack(pady=2)
        ttk.Button(preset_frame, text="平衡模式", width=12,
                  command=lambda: self.set_preset_params('balanced')).pack(pady=2)
        ttk.Button(preset_frame, text="高精确度", width=12,
                  command=lambda: self.set_preset_params('high_precision')).pack(pady=2)
        
    def create_log_and_control_section(self, parent, row):
        """创建日志和控制区域"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=row, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        control_frame.columnconfigure(0, weight=1)
        control_frame.rowconfigure(1, weight=1)
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(button_frame, text="保存配置", 
                  command=self.save_detection_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="加载配置", 
                  command=self.load_detection_config).pack(side=tk.LEFT, padx=5)
        
        # 运行按钮（突出显示）
        run_button = ttk.Button(button_frame, text="开始检测", 
                               command=self.run_detection_task)
        run_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 配置按钮样式
        style = ttk.Style()
        style.configure('Accent.TButton', font=('Arial', 10, 'bold'))
        run_button.configure(style='Accent.TButton')
        
        # 日志区域
        log_frame = ttk.LabelFrame(control_frame, text="运行日志", padding="5")
        log_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        ttk.Button(log_button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_button_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(log_button_frame, variable=self.progress_var, 
                                          mode='determinate', length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
    def create_processing_tab(self):
        """创建后处理选项卡"""
        processing_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(processing_frame, text="后处理")
        
        # 配置网格权重
        processing_frame.columnconfigure(0, weight=1)
        processing_frame.rowconfigure(4, weight=1)
        
        # 分割配置
        seg_frame = ttk.LabelFrame(processing_frame, text="结节分割", padding="10")
        seg_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        seg_frame.columnconfigure(1, weight=1)
        
        ttk.Label(seg_frame, text="分割输出目录:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(seg_frame, textvariable=self.config_vars['segmentation_output_dir'], width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(seg_frame, text="浏览", 
                  command=lambda: self.browse_directory('segmentation_output_dir')).grid(
            row=0, column=2, padx=(5, 0), pady=2)
        
        ttk.Button(seg_frame, text="运行分割", 
                  command=self.run_segmentation).grid(row=1, column=0, pady=(10, 0))
        
        # 分类配置
        cls_frame = ttk.LabelFrame(processing_frame, text="良恶性分类", padding="10")
        cls_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(cls_frame, text="分类模型:").grid(row=0, column=0, sticky=tk.W, pady=2)
        
        model_combo = ttk.Combobox(cls_frame, textvariable=self.config_vars['classification_model'],
                                  values=['SWS++', '3D ResNet50', 'Model Genesis', 'MedicalNet3D', 'FMCB'],
                                  state='readonly', width=20)
        model_combo.grid(row=0, column=1, sticky=tk.W, padx=(5, 0), pady=2)
        
        ttk.Button(cls_frame, text="运行分类", 
                  command=self.run_classification).grid(row=1, column=0, pady=(10, 0))
        
        # 影像组学配置
        rad_frame = ttk.LabelFrame(processing_frame, text="影像组学特征提取", padding="10")
        rad_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        rad_frame.columnconfigure(1, weight=1)
        
        ttk.Label(rad_frame, text="特征输出目录:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(rad_frame, textvariable=self.config_vars['radiomics_output_dir'], width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(rad_frame, text="浏览", 
                  command=lambda: self.browse_directory('radiomics_output_dir')).grid(
            row=0, column=2, padx=(5, 0), pady=2)
        
        ttk.Button(rad_frame, text="提取特征", 
                  command=self.run_radiomics).grid(row=1, column=0, pady=(10, 0))
        
        # 可视化配置
        vis_frame = ttk.LabelFrame(processing_frame, text="结果可视化", padding="10")
        vis_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        vis_frame.columnconfigure(1, weight=1)
        
        ttk.Label(vis_frame, text="可视化输出目录:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(vis_frame, textvariable=self.config_vars['visualization_output_dir'], width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(vis_frame, text="浏览", 
                  command=lambda: self.browse_directory('visualization_output_dir')).grid(
            row=0, column=2, padx=(5, 0), pady=2)
        
        vis_button_frame = ttk.Frame(vis_frame)
        vis_button_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(vis_button_frame, text="生成可视化", 
                  command=self.run_visualization).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(vis_button_frame, text="生成报告", 
                  command=self.generate_report).pack(side=tk.LEFT, padx=5)
        
        # 处理日志
        process_log_frame = ttk.LabelFrame(processing_frame, text="处理日志", padding="5")
        process_log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        process_log_frame.columnconfigure(0, weight=1)
        process_log_frame.rowconfigure(0, weight=1)
        
        self.process_log_text = scrolledtext.ScrolledText(process_log_frame, height=10)
        self.process_log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def create_batch_tab(self):
        """创建批量处理选项卡"""
        batch_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(batch_frame, text="批量处理")

        # 批量任务配置
        task_frame = ttk.LabelFrame(batch_frame, text="批量任务配置", padding="10")
        task_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        task_frame.columnconfigure(1, weight=1)

        # 任务选择
        ttk.Label(task_frame, text="处理任务:").grid(row=0, column=0, sticky=tk.W, pady=2)

        self.batch_tasks = {
            'detection': tk.BooleanVar(value=True),
            'segmentation': tk.BooleanVar(value=False),
            'classification': tk.BooleanVar(value=False),
            'radiomics': tk.BooleanVar(value=False),
            'visualization': tk.BooleanVar(value=False)
        }

        task_check_frame = ttk.Frame(task_frame)
        task_check_frame.grid(row=0, column=1, sticky=tk.W, padx=(5, 0), pady=2)

        ttk.Checkbutton(task_check_frame, text="检测", variable=self.batch_tasks['detection']).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Checkbutton(task_check_frame, text="分割", variable=self.batch_tasks['segmentation']).pack(side=tk.LEFT, padx=10)
        ttk.Checkbutton(task_check_frame, text="分类", variable=self.batch_tasks['classification']).pack(side=tk.LEFT, padx=10)
        ttk.Checkbutton(task_check_frame, text="特征提取", variable=self.batch_tasks['radiomics']).pack(side=tk.LEFT, padx=10)
        ttk.Checkbutton(task_check_frame, text="可视化", variable=self.batch_tasks['visualization']).pack(side=tk.LEFT, padx=10)

        # 批量文件列表
        files_frame = ttk.LabelFrame(batch_frame, text="批量文件列表", padding="10")
        files_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        files_frame.columnconfigure(0, weight=1)
        files_frame.rowconfigure(0, weight=1)

        # 文件树视图
        self.batch_tree = ttk.Treeview(files_frame, columns=('path', 'status', 'progress'), show='tree headings')
        self.batch_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # 配置列
        self.batch_tree.heading('#0', text='文件名')
        self.batch_tree.heading('path', text='路径')
        self.batch_tree.heading('status', text='状态')
        self.batch_tree.heading('progress', text='进度')

        self.batch_tree.column('#0', width=200)
        self.batch_tree.column('path', width=300)
        self.batch_tree.column('status', width=100)
        self.batch_tree.column('progress', width=100)

        # 滚动条
        batch_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.batch_tree.yview)
        batch_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.batch_tree.config(yscrollcommand=batch_scrollbar.set)

        # 批量操作按钮
        batch_button_frame = ttk.Frame(batch_frame)
        batch_button_frame.grid(row=2, column=0, pady=(0, 10))

        ttk.Button(batch_button_frame, text="添加文件",
                  command=self.add_batch_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_button_frame, text="添加目录",
                  command=self.add_batch_directory).pack(side=tk.LEFT, padx=5)
        ttk.Button(batch_button_frame, text="移除选中",
                  command=self.remove_batch_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(batch_button_frame, text="清空列表",
                  command=self.clear_batch_list).pack(side=tk.LEFT, padx=5)

        ttk.Button(batch_button_frame, text="开始批量处理",
                  command=self.start_batch_processing).pack(side=tk.RIGHT, padx=(10, 0))

        # 批量处理进度
        progress_frame = ttk.LabelFrame(batch_frame, text="批量处理进度", padding="10")
        progress_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(1, weight=1)

        ttk.Label(progress_frame, text="总体进度:").grid(row=0, column=0, sticky=tk.W, pady=2)

        self.batch_progress_var = tk.DoubleVar()
        self.batch_progress_bar = ttk.Progressbar(progress_frame, variable=self.batch_progress_var,
                                                mode='determinate', length=300)
        self.batch_progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)

        self.batch_progress_label = ttk.Label(progress_frame, text="0/0")
        self.batch_progress_label.grid(row=0, column=2, padx=(5, 0), pady=2)

    def create_results_tab(self):
        """创建结果查看选项卡"""
        results_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(results_frame, text="结果查看")

        # 结果文件浏览
        browse_frame = ttk.LabelFrame(results_frame, text="结果文件浏览", padding="10")
        browse_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        browse_frame.columnconfigure(1, weight=1)

        ttk.Label(browse_frame, text="结果文件:").grid(row=0, column=0, sticky=tk.W, pady=2)

        self.result_file_var = tk.StringVar()
        ttk.Entry(browse_frame, textvariable=self.result_file_var, width=60).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(browse_frame, text="浏览",
                  command=self.browse_result_file).grid(row=0, column=2, padx=(5, 0), pady=2)

        # 结果操作按钮
        result_button_frame = ttk.Frame(browse_frame)
        result_button_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0))

        ttk.Button(result_button_frame, text="加载结果",
                  command=self.load_results).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(result_button_frame, text="生成可视化",
                  command=self.visualize_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(result_button_frame, text="导出报告",
                  command=self.export_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(result_button_frame, text="打开输出目录",
                  command=self.open_output_directory).pack(side=tk.LEFT, padx=5)

        # 结果统计显示
        stats_frame = ttk.LabelFrame(results_frame, text="检测统计", padding="10")
        stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        stats_frame.columnconfigure(1, weight=1)

        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=8, width=80)
        self.stats_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))

        # 结果详情显示
        details_frame = ttk.LabelFrame(results_frame, text="详细结果", padding="10")
        details_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(0, weight=1)

        # 结果树视图
        self.results_tree = ttk.Treeview(details_frame, columns=('image', 'nodules', 'max_score', 'avg_score'), show='tree headings')
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # 配置列
        self.results_tree.heading('#0', text='序号')
        self.results_tree.heading('image', text='图像文件')
        self.results_tree.heading('nodules', text='结节数量')
        self.results_tree.heading('max_score', text='最高置信度')
        self.results_tree.heading('avg_score', text='平均置信度')

        self.results_tree.column('#0', width=60)
        self.results_tree.column('image', width=300)
        self.results_tree.column('nodules', width=80)
        self.results_tree.column('max_score', width=100)
        self.results_tree.column('avg_score', width=100)

        # 滚动条
        results_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        results_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.results_tree.config(yscrollcommand=results_scrollbar.set)

    def create_settings_tab(self):
        """创建设置选项卡"""
        settings_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(settings_frame, text="系统设置")

        # 环境设置
        env_frame = ttk.LabelFrame(settings_frame, text="环境设置", padding="10")
        env_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        env_frame.columnconfigure(1, weight=1)

        ttk.Label(env_frame, text="当前环境:").grid(row=0, column=0, sticky=tk.W, pady=2)

        self.current_env_label = ttk.Label(env_frame, text="检测中...", foreground='blue')
        self.current_env_label.grid(row=0, column=1, sticky=tk.W, padx=(5, 0), pady=2)

        ttk.Button(env_frame, text="检查环境",
                  command=self.check_environment).grid(row=0, column=2, padx=(10, 0), pady=2)

        # 路径设置
        path_frame = ttk.LabelFrame(settings_frame, text="默认路径设置", padding="10")
        path_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        path_frame.columnconfigure(1, weight=1)

        self.default_paths = {
            'models_dir': tk.StringVar(value='models/'),
            'data_dir': tk.StringVar(value='data/'),
            'output_dir': tk.StringVar(value='output/')
        }

        ttk.Label(path_frame, text="模型目录:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(path_frame, textvariable=self.default_paths['models_dir'], width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(path_frame, text="浏览",
                  command=lambda: self.browse_directory_for_var('models_dir')).grid(
            row=0, column=2, padx=(5, 0), pady=2)

        ttk.Label(path_frame, text="数据目录:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(path_frame, textvariable=self.default_paths['data_dir'], width=50).grid(
            row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(path_frame, text="浏览",
                  command=lambda: self.browse_directory_for_var('data_dir')).grid(
            row=1, column=2, padx=(5, 0), pady=2)

        ttk.Label(path_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(path_frame, textvariable=self.default_paths['output_dir'], width=50).grid(
            row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(path_frame, text="浏览",
                  command=lambda: self.browse_directory_for_var('output_dir')).grid(
            row=2, column=2, padx=(5, 0), pady=2)

        # 系统信息
        info_frame = ttk.LabelFrame(settings_frame, text="系统信息", padding="10")
        info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)

        self.info_text = scrolledtext.ScrolledText(info_frame, height=15, width=80)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 初始化系统信息
        self.update_system_info()

        # 检查当前环境
        self.check_environment()

    # ==================== 文件浏览方法 ====================

    def browse_file(self, var_name, file_desc, file_types):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title=f"选择{file_desc}",
            filetypes=[(file_desc, file_types), ("所有文件", "*.*")]
        )
        if filename:
            self.config_vars[var_name].set(filename)

    def browse_save_file(self, var_name, file_desc, file_types):
        """浏览保存文件"""
        filename = filedialog.asksaveasfilename(
            title=f"保存{file_desc}",
            filetypes=[(file_desc, file_types), ("所有文件", "*.*")]
        )
        if filename:
            self.config_vars[var_name].set(filename)

    def browse_directory(self, var_name):
        """浏览目录"""
        directory = filedialog.askdirectory(title="选择目录")
        if directory:
            self.config_vars[var_name].set(directory)

    def browse_directory_for_var(self, var_name):
        """为默认路径变量浏览目录"""
        directory = filedialog.askdirectory(title="选择目录")
        if directory:
            self.default_paths[var_name].set(directory)

    def browse_json_file(self):
        """浏览JSON文件"""
        filename = filedialog.askopenfilename(
            title="选择JSON数据列表文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            self.json_entry.delete(0, tk.END)
            self.json_entry.insert(0, filename)

    def browse_result_file(self):
        """浏览结果文件"""
        filename = filedialog.askopenfilename(
            title="选择结果文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            self.result_file_var.set(filename)

    # ==================== 数据选择方法 ====================

    def on_data_mode_change(self):
        """数据选择模式改变时的处理"""
        mode = self.config_vars['data_selection_mode'].get()

        if mode == 'files':
            # 显示文件选择界面，隐藏JSON界面
            self.files_frame.grid()
            self.json_frame.grid_remove()
        else:
            # 显示JSON界面，隐藏文件选择界面
            self.files_frame.grid_remove()
            self.json_frame.grid()

    def add_image_files(self):
        """添加图像文件"""
        filenames = filedialog.askopenfilenames(
            title="选择图像文件",
            filetypes=[
                ("医学图像", "*.nii *.nii.gz *.dcm *.mhd *.nrrd"),
                ("NIfTI文件", "*.nii *.nii.gz"),
                ("DICOM文件", "*.dcm"),
                ("所有文件", "*.*")
            ]
        )

        for filename in filenames:
            if filename not in self.config_vars['selected_files']:
                self.config_vars['selected_files'].append(filename)
                self.files_listbox.insert(tk.END, os.path.basename(filename))

    def add_image_directory(self):
        """添加图像目录"""
        directory = filedialog.askdirectory(title="选择图像目录")
        if directory:
            # 扫描目录中的图像文件
            image_extensions = ['.nii', '.nii.gz', '.dcm', '.mhd', '.nrrd']

            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        filepath = os.path.join(root, file)
                        if filepath not in self.config_vars['selected_files']:
                            self.config_vars['selected_files'].append(filepath)
                            self.files_listbox.insert(tk.END, os.path.relpath(filepath, directory))

    def remove_selected_files(self):
        """移除选中的文件"""
        selected_indices = self.files_listbox.curselection()

        # 从后往前删除，避免索引变化
        for index in reversed(selected_indices):
            self.files_listbox.delete(index)
            del self.config_vars['selected_files'][index]

    def clear_file_list(self):
        """清空文件列表"""
        self.files_listbox.delete(0, tk.END)
        self.config_vars['selected_files'].clear()

    # ==================== 配置管理方法 ====================

    def set_default_paths(self):
        """设置默认路径"""
        # 设置一些常用的默认路径
        base_dir = os.getcwd()

        # 模型路径
        model_path = os.path.join(base_dir, "models", "LUNA16_mD.pt")
        if os.path.exists(model_path):
            self.config_vars['model_path'].set(model_path)

        # 数据目录
        data_dir = os.path.join(base_dir, "data")
        if os.path.exists(data_dir):
            self.config_vars['data_base_dir'].set(data_dir)

        # 输出路径
        output_dir = os.path.join(base_dir, "output")
        os.makedirs(output_dir, exist_ok=True)
        result_file = os.path.join(output_dir, "detection_results.json")
        self.config_vars['result_list_file_path'].set(result_file)

        self.log_message("已设置默认路径")

    def set_preset_params(self, preset_type):
        """设置预设参数"""
        if preset_type == 'high_sensitivity':
            # 高敏感度：低阈值，检测更多结节
            self.config_vars['score_thresh'].set(0.01)
            self.config_vars['nms_thresh'].set(0.3)
            self.log_message("已设置高敏感度参数")

        elif preset_type == 'balanced':
            # 平衡模式：默认参数
            self.config_vars['score_thresh'].set(0.02)
            self.config_vars['nms_thresh'].set(0.22)
            self.log_message("已设置平衡模式参数")

        elif preset_type == 'high_precision':
            # 高精确度：高阈值，减少假阳性
            self.config_vars['score_thresh'].set(0.05)
            self.config_vars['nms_thresh'].set(0.15)
            self.log_message("已设置高精确度参数")

    def validate_detection_config(self):
        """验证检测配置"""
        errors = []

        # 检查模型文件
        model_path = self.config_vars['model_path'].get()
        if not model_path or not os.path.exists(model_path):
            errors.append("模型文件不存在")

        # 检查数据
        if self.config_vars['data_selection_mode'].get() == 'files':
            if not self.config_vars['selected_files']:
                errors.append("未选择任何图像文件")
        else:
            json_file = self.json_entry.get()
            if not json_file or not os.path.exists(json_file):
                errors.append("JSON数据列表文件不存在")

        # 检查输出路径
        result_path = self.config_vars['result_list_file_path'].get()
        if not result_path:
            errors.append("未设置结果输出路径")
        else:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(result_path), exist_ok=True)

        if errors:
            messagebox.showerror("配置验证失败", "\n".join(errors))
            return False
        else:
            messagebox.showinfo("配置验证成功", "所有配置项验证通过")
            return True

    def save_detection_config(self):
        """保存检测配置"""
        config = {
            'model_path': self.config_vars['model_path'].get(),
            'data_base_dir': self.config_vars['data_base_dir'].get(),
            'result_list_file_path': self.config_vars['result_list_file_path'].get(),
            'score_thresh': self.config_vars['score_thresh'].get(),
            'nms_thresh': self.config_vars['nms_thresh'].get(),
            'batch_size': self.config_vars['batch_size'].get(),
            'patch_size': [
                self.config_vars['patch_size_x'].get(),
                self.config_vars['patch_size_y'].get(),
                self.config_vars['patch_size_z'].get()
            ],
            'val_patch_size': [
                self.config_vars['val_patch_size_x'].get(),
                self.config_vars['val_patch_size_y'].get(),
                self.config_vars['val_patch_size_z'].get()
            ],
            'data_selection_mode': self.config_vars['data_selection_mode'].get(),
            'selected_files': self.config_vars['selected_files'],
            'json_file': self.json_entry.get() if self.config_vars['data_selection_mode'].get() == 'json' else ''
        }

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                self.log_message(f"配置已保存到: {filename}")
                messagebox.showinfo("保存成功", f"配置已保存到:\n{filename}")
            except Exception as e:
                self.log_message(f"保存配置失败: {str(e)}")
                messagebox.showerror("保存失败", f"保存配置失败:\n{str(e)}")

    def load_detection_config(self):
        """加载检测配置"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载配置到界面
                self.config_vars['model_path'].set(config.get('model_path', ''))
                self.config_vars['data_base_dir'].set(config.get('data_base_dir', ''))
                self.config_vars['result_list_file_path'].set(config.get('result_list_file_path', ''))
                self.config_vars['score_thresh'].set(config.get('score_thresh', 0.02))
                self.config_vars['nms_thresh'].set(config.get('nms_thresh', 0.22))
                self.config_vars['batch_size'].set(config.get('batch_size', 1))

                # 加载patch size
                patch_size = config.get('patch_size', [192, 192, 80])
                self.config_vars['patch_size_x'].set(patch_size[0])
                self.config_vars['patch_size_y'].set(patch_size[1])
                self.config_vars['patch_size_z'].set(patch_size[2])

                val_patch_size = config.get('val_patch_size', [512, 512, 208])
                self.config_vars['val_patch_size_x'].set(val_patch_size[0])
                self.config_vars['val_patch_size_y'].set(val_patch_size[1])
                self.config_vars['val_patch_size_z'].set(val_patch_size[2])

                # 加载数据选择模式
                self.config_vars['data_selection_mode'].set(config.get('data_selection_mode', 'files'))
                self.config_vars['selected_files'] = config.get('selected_files', [])

                # 更新文件列表显示
                self.files_listbox.delete(0, tk.END)
                for file_path in self.config_vars['selected_files']:
                    self.files_listbox.insert(tk.END, os.path.basename(file_path))

                # 更新JSON文件路径
                json_file = config.get('json_file', '')
                self.json_entry.delete(0, tk.END)
                self.json_entry.insert(0, json_file)

                # 更新界面显示
                self.on_data_mode_change()

                self.log_message(f"配置已从 {filename} 加载")
                messagebox.showinfo("加载成功", f"配置已从以下文件加载:\n{filename}")

            except Exception as e:
                self.log_message(f"加载配置失败: {str(e)}")
                messagebox.showerror("加载失败", f"加载配置失败:\n{str(e)}")

    def load_existing_config(self):
        """加载现有配置（如果存在）"""
        config_file = "detection_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 只加载基本配置，不覆盖默认值
                if 'model_path' in config and config['model_path']:
                    self.config_vars['model_path'].set(config['model_path'])
                if 'data_base_dir' in config and config['data_base_dir']:
                    self.config_vars['data_base_dir'].set(config['data_base_dir'])

                self.log_message("已加载现有配置文件")
            except:
                pass  # 忽略加载错误

    # ==================== 任务执行方法 ====================

    def run_detection_task(self):
        """运行检测任务"""
        if not self.validate_detection_config():
            return

        # 准备数据列表
        if self.config_vars['data_selection_mode'].get() == 'files':
            # 使用选择的文件
            if not self.config_vars['selected_files']:
                messagebox.showerror("错误", "未选择任何文件")
                return

            # 创建临时JSON文件
            temp_json = "temp_detection_list.json"
            data_list = []

            for file_path in self.config_vars['selected_files']:
                # 构建相对于data_base_dir的路径
                base_dir = self.config_vars['data_base_dir'].get()
                if base_dir and file_path.startswith(base_dir):
                    relative_path = os.path.relpath(file_path, base_dir)
                else:
                    relative_path = file_path

                data_list.append({
                    "image_path": relative_path,
                    "label_path": "",  # 检测任务不需要标签
                    "spacing": [1.0, 1.0, 1.0]  # 默认spacing
                })

            try:
                with open(temp_json, 'w', encoding='utf-8') as f:
                    json.dump(data_list, f, indent=2, ensure_ascii=False)

                test_data_file = temp_json

            except Exception as e:
                messagebox.showerror("错误", f"创建数据列表失败:\n{str(e)}")
                return
        else:
            # 使用JSON文件
            test_data_file = self.json_entry.get()

        # 创建环境配置文件
        env_config = {
            "model_path": self.config_vars['model_path'].get(),
            "data_list_file_path": test_data_file,
            "data_base_dir": self.config_vars['data_base_dir'].get(),
            "result_list_file_path": self.config_vars['result_list_file_path'].get()
        }

        # 创建测试配置文件
        test_config = {
            "score_thresh": self.config_vars['score_thresh'].get(),
            "nms_thresh": self.config_vars['nms_thresh'].get(),
            "batch_size": self.config_vars['batch_size'].get(),
            "patch_size": [
                self.config_vars['patch_size_x'].get(),
                self.config_vars['patch_size_y'].get(),
                self.config_vars['patch_size_z'].get()
            ],
            "val_patch_size": [
                self.config_vars['val_patch_size_x'].get(),
                self.config_vars['val_patch_size_y'].get(),
                self.config_vars['val_patch_size_z'].get()
            ],
            "gt_box_mode": "cccwhd",
            "amp": True,
            "num_workers": 4,
            "device": "cuda:0" if (TORCH_AVAILABLE and torch.cuda.is_available()) else "cpu"
        }

        # 保存临时配置文件
        temp_env_config = "temp_env_config.json"
        temp_test_config = "temp_test_config.json"

        try:
            with open(temp_env_config, 'w', encoding='utf-8') as f:
                json.dump(env_config, f, indent=2, ensure_ascii=False)

            with open(temp_test_config, 'w', encoding='utf-8') as f:
                json.dump(test_config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            messagebox.showerror("错误", f"创建配置文件失败:\n{str(e)}")
            return

        # 构建命令 - 使用简化的test.py
        cmd = [
            sys.executable, "ct_detection/test.py",
            "--environment-file", temp_env_config,
            "--config-file", temp_test_config
        ]

        self.log_message("开始检测任务...")
        self.log_message(f"命令: {' '.join(cmd)}")

        # 在新线程中运行任务
        self.current_task = threading.Thread(target=self._run_command, args=(cmd, "检测"))
        self.current_task.daemon = True
        self.current_task.start()

        # 启动进度监控
        self.monitor_progress()

    def _run_command(self, cmd, task_name):
        """在后台运行命令"""
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 实时读取输出
            for line in process.stdout:
                line = line.strip()
                if line:
                    self.log_queue.put(f"[{task_name}] {line}")

            process.wait()

            if process.returncode == 0:
                self.log_queue.put(f"[{task_name}] 任务完成")
                # 如果是检测任务，清理临时文件
                if task_name == "检测":
                    temp_files = [
                        "temp_detection_list.json",
                        "temp_env_config.json",
                        "temp_test_config.json"
                    ]
                    for temp_file in temp_files:
                        if os.path.exists(temp_file):
                            try:
                                os.remove(temp_file)
                                self.log_queue.put(f"[{task_name}] 已清理临时文件: {temp_file}")
                            except Exception as e:
                                self.log_queue.put(f"[{task_name}] 清理临时文件失败: {temp_file}, {str(e)}")
            else:
                self.log_queue.put(f"[{task_name}] 任务失败，返回码: {process.returncode}")

        except Exception as e:
            self.log_queue.put(f"[{task_name}] 执行错误: {str(e)}")

    def monitor_progress(self):
        """监控任务进度"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_message(message)
        except queue.Empty:
            pass

        # 继续监控
        self.root.after(100, self.monitor_progress)

    # ==================== 其他功能方法 ====================

    def run_segmentation(self):
        """运行分割任务"""
        self.log_message("开始结节分割任务...")

        # 检查检测结果文件
        result_file = self.config_vars['result_list_file_path'].get()
        if not os.path.exists(result_file):
            messagebox.showerror("错误", "请先运行检测任务生成结果文件")
            return

        # 构建分割命令
        cmd = [
            sys.executable, "segmentation_3d_fixed/test.py",
            "--detection_results", result_file,
            "--output_dir", self.config_vars['segmentation_output_dir'].get()
        ]

        self.process_log_message("开始分割任务...")

        # 在新线程中运行
        task = threading.Thread(target=self._run_command, args=(cmd, "分割"))
        task.daemon = True
        task.start()

    def run_classification(self):
        """运行分类任务"""
        self.log_message("开始良恶性分类任务...")

        model_name = self.config_vars['classification_model'].get()

        # 构建分类命令
        cmd = [
            sys.executable, f"classification/{model_name}/test.py",
            "--detection_results", self.config_vars['result_list_file_path'].get()
        ]

        self.process_log_message(f"开始{model_name}分类任务...")

        # 在新线程中运行
        task = threading.Thread(target=self._run_command, args=(cmd, "分类"))
        task.daemon = True
        task.start()

    def run_radiomics(self):
        """运行影像组学特征提取"""
        self.log_message("开始影像组学特征提取...")

        # 构建特征提取命令
        cmd = [
            sys.executable, "radiomics_feature_extraction/extract_features.py",
            "--detection_results", self.config_vars['result_list_file_path'].get(),
            "--output_dir", self.config_vars['radiomics_output_dir'].get()
        ]

        self.process_log_message("开始特征提取任务...")

        # 在新线程中运行
        task = threading.Thread(target=self._run_command, args=(cmd, "特征提取"))
        task.daemon = True
        task.start()

    def run_visualization(self):
        """运行可视化"""
        self.log_message("开始生成可视化...")

        # 构建可视化命令
        cmd = [
            sys.executable, "visualization/generate_visualizations.py",
            "--detection_results", self.config_vars['result_list_file_path'].get(),
            "--output_dir", self.config_vars['visualization_output_dir'].get()
        ]

        self.process_log_message("开始可视化任务...")

        # 在新线程中运行
        task = threading.Thread(target=self._run_command, args=(cmd, "可视化"))
        task.daemon = True
        task.start()

    def generate_report(self):
        """生成报告"""
        self.log_message("开始生成报告...")

        # 构建报告生成命令
        cmd = [
            sys.executable, "report_generation/generate_report.py",
            "--detection_results", self.config_vars['result_list_file_path'].get()
        ]

        self.process_log_message("开始报告生成...")

        # 在新线程中运行
        task = threading.Thread(target=self._run_command, args=(cmd, "报告生成"))
        task.daemon = True
        task.start()

    # ==================== 批量处理方法 ====================

    def add_batch_files(self):
        """添加批量处理文件"""
        filenames = filedialog.askopenfilenames(
            title="选择批量处理文件",
            filetypes=[
                ("医学图像", "*.nii *.nii.gz *.dcm *.mhd *.nrrd"),
                ("所有文件", "*.*")
            ]
        )

        for filename in filenames:
            # 添加到批量处理树
            item_id = self.batch_tree.insert('', 'end',
                                           text=os.path.basename(filename),
                                           values=(filename, '待处理', '0%'))

    def add_batch_directory(self):
        """添加批量处理目录"""
        directory = filedialog.askdirectory(title="选择批量处理目录")
        if directory:
            # 扫描目录中的图像文件
            image_extensions = ['.nii', '.nii.gz', '.dcm', '.mhd', '.nrrd']

            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        filepath = os.path.join(root, file)
                        item_id = self.batch_tree.insert('', 'end',
                                                       text=file,
                                                       values=(filepath, '待处理', '0%'))

    def remove_batch_files(self):
        """移除批量处理文件"""
        selected_items = self.batch_tree.selection()
        for item in selected_items:
            self.batch_tree.delete(item)

    def clear_batch_list(self):
        """清空批量处理列表"""
        for item in self.batch_tree.get_children():
            self.batch_tree.delete(item)

    def start_batch_processing(self):
        """开始批量处理"""
        items = self.batch_tree.get_children()
        if not items:
            messagebox.showwarning("警告", "批量处理列表为空")
            return

        # 获取选中的任务
        selected_tasks = []
        for task_name, var in self.batch_tasks.items():
            if var.get():
                selected_tasks.append(task_name)

        if not selected_tasks:
            messagebox.showwarning("警告", "未选择任何处理任务")
            return

        self.log_message(f"开始批量处理，共{len(items)}个文件，任务：{', '.join(selected_tasks)}")

        # 在新线程中运行批量处理
        task = threading.Thread(target=self._run_batch_processing, args=(items, selected_tasks))
        task.daemon = True
        task.start()

    def _run_batch_processing(self, items, selected_tasks):
        """执行批量处理"""
        total_items = len(items)

        for i, item in enumerate(items):
            file_path = self.batch_tree.item(item)['values'][0]
            file_name = self.batch_tree.item(item)['text']

            self.log_queue.put(f"[批量处理] 处理文件 {i+1}/{total_items}: {file_name}")

            # 更新状态
            self.batch_tree.item(item, values=(file_path, '处理中', f"{i*100//total_items}%"))

            try:
                # 执行选中的任务
                for task in selected_tasks:
                    if task == 'detection':
                        # 执行检测
                        pass
                    elif task == 'segmentation':
                        # 执行分割
                        pass
                    # ... 其他任务

                # 更新完成状态
                self.batch_tree.item(item, values=(file_path, '已完成', '100%'))

            except Exception as e:
                self.log_queue.put(f"[批量处理] 处理文件失败: {file_name}, 错误: {str(e)}")
                self.batch_tree.item(item, values=(file_path, '失败', '0%'))

            # 更新总体进度
            progress = (i + 1) * 100 / total_items
            self.batch_progress_var.set(progress)
            self.batch_progress_label.config(text=f"{i+1}/{total_items}")

        self.log_queue.put("[批量处理] 批量处理完成")

    # ==================== 结果查看方法 ====================

    def load_results(self):
        """加载检测结果"""
        result_file = self.result_file_var.get()
        if not result_file or not os.path.exists(result_file):
            messagebox.showerror("错误", "结果文件不存在")
            return

        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            # 清空现有结果
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # 统计信息
            total_images = len(results)
            total_nodules = 0
            all_scores = []

            # 添加结果到树视图
            for i, result in enumerate(results):
                image_path = result.get('image_path', '')
                nodules = result.get('nodules', [])
                nodule_count = len(nodules)
                total_nodules += nodule_count

                if nodules:
                    scores = [n.get('score', 0) for n in nodules]
                    max_score = max(scores)
                    avg_score = sum(scores) / len(scores)
                    all_scores.extend(scores)
                else:
                    max_score = 0
                    avg_score = 0

                self.results_tree.insert('', 'end',
                                       text=str(i+1),
                                       values=(os.path.basename(image_path),
                                             nodule_count,
                                             f"{max_score:.3f}",
                                             f"{avg_score:.3f}"))

            # 更新统计信息
            stats_text = f"""检测结果统计：

总图像数量: {total_images}
总结节数量: {total_nodules}
平均每图结节数: {total_nodules/total_images:.2f}

置信度统计:
最高置信度: {max(all_scores):.3f} (如果有结节)
最低置信度: {min(all_scores):.3f} (如果有结节)
平均置信度: {sum(all_scores)/len(all_scores):.3f} (如果有结节)

检测阈值分布:
>= 0.5: {len([s for s in all_scores if s >= 0.5])}
>= 0.3: {len([s for s in all_scores if s >= 0.3])}
>= 0.1: {len([s for s in all_scores if s >= 0.1])}
< 0.1: {len([s for s in all_scores if s < 0.1])}
"""

            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)

            self.log_message(f"已加载检测结果: {total_images}个图像, {total_nodules}个结节")

        except Exception as e:
            messagebox.showerror("错误", f"加载结果失败:\n{str(e)}")

    def visualize_results(self):
        """可视化结果"""
        result_file = self.result_file_var.get()
        if not result_file:
            messagebox.showerror("错误", "请先选择结果文件")
            return

        self.run_visualization()

    def export_report(self):
        """导出报告"""
        result_file = self.result_file_var.get()
        if not result_file:
            messagebox.showerror("错误", "请先选择结果文件")
            return

        self.generate_report()

    def open_output_directory(self):
        """打开输出目录"""
        result_file = self.config_vars['result_list_file_path'].get()
        if result_file:
            output_dir = os.path.dirname(result_file)
            if os.path.exists(output_dir):
                os.startfile(output_dir)  # Windows
            else:
                messagebox.showwarning("警告", "输出目录不存在")
        else:
            messagebox.showwarning("警告", "未设置输出路径")

    # ==================== 系统设置方法 ====================

    def check_environment(self):
        """检查当前环境"""
        try:
            # 检查Python版本
            python_version = sys.version

            # 检查conda环境
            conda_env = os.environ.get('CONDA_DEFAULT_ENV', '未知')

            # 检查关键包
            try:
                import torch
                torch_version = torch.__version__
                cuda_available = torch.cuda.is_available()
            except ImportError:
                torch_version = "未安装"
                cuda_available = False

            try:
                import numpy
                numpy_version = numpy.__version__
            except ImportError:
                numpy_version = "未安装"

            # 更新环境标签
            env_info = f"Conda环境: {conda_env}"
            if cuda_available:
                env_info += " (CUDA可用)"
            else:
                env_info += " (仅CPU)"

            self.current_env_label.config(text=env_info)

            # 更新系统信息
            system_info = f"""系统环境信息：

Python版本: {python_version}

Conda环境: {conda_env}

关键依赖包:
- PyTorch: {torch_version}
- NumPy: {numpy_version}
- CUDA可用: {'是' if cuda_available else '否'}

工作目录: {os.getcwd()}

环境变量:
- CONDA_DEFAULT_ENV: {os.environ.get('CONDA_DEFAULT_ENV', '未设置')}
- CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', '未设置')}
"""

            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, system_info)

        except Exception as e:
            self.current_env_label.config(text=f"检查失败: {str(e)}")

    def update_system_info(self):
        """更新系统信息"""
        try:
            import platform

            system_info = f"""系统信息：

操作系统: {platform.system()} {platform.release()}
处理器: {platform.processor()}
Python版本: {platform.python_version()}
Python实现: {platform.python_implementation()}

当前工作目录: {os.getcwd()}
脚本路径: {os.path.abspath(__file__)}

可用内存: 检测中...
磁盘空间: 检测中...
"""

            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, system_info)

        except Exception as e:
            self.info_text.insert(1.0, f"获取系统信息失败: {str(e)}")

    # ==================== 日志方法 ====================

    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

    def process_log_message(self, message):
        """记录处理日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.process_log_text.insert(tk.END, log_entry)
        self.process_log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        log_content = self.log_text.get(1.0, tk.END)

        filename = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                messagebox.showinfo("保存成功", f"日志已保存到:\n{filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存日志失败:\n{str(e)}")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = IntegratedLungDetectionGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass  # 忽略图标加载错误

    # 设置窗口关闭事件
    def on_closing():
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
