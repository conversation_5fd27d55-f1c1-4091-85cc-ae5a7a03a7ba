import argparse
import json
import os
import sys
import time
import numpy as np
import torch
from itertools import chain
import pandas as pd
import logging
import nibabel as nib
from monai.transforms import (
    Compose,
    LoadImaged,
    EnsureChannelFirstd,
    Orientationd,
    ScaleIntensityRanged,
    Resized,
    EnsureTyped
)

# 添加分类模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, "ct_classification"))

# 导入分类所需模块
from ct_classification.classification_models import get_classification_model


def extract_nodule_patch(image_path, box, patch_size=[64, 64, 64]):
    """
    从原始CT图像中提取结节区域
    
    Args:
        image_path: 原始CT图像路径
        box: 结节边界框 [x1, y1, z1, x2, y2, z2]
        patch_size: 提取的块大小
    
    Returns:
        nodule_patch: 提取的结节区域张量
    """
    # 加载原始图像
    img_nib = nib.load(image_path)
    img_data = img_nib.get_fdata()
    affine = img_nib.affine
    
    # 计算结节中心点
    center_x = (box[0] + box[3]) / 2
    center_y = (box[1] + box[4]) / 2
    center_z = (box[2] + box[5]) / 2
    
    # 计算提取区域的边界
    half_size = [p // 2 for p in patch_size]
    x_min = max(0, int(center_x - half_size[0]))
    y_min = max(0, int(center_y - half_size[1]))
    z_min = max(0, int(center_z - half_size[2]))
    
    x_max = min(img_data.shape[0], x_min + patch_size[0])
    y_max = min(img_data.shape[1], y_min + patch_size[1])
    z_max = min(img_data.shape[2], z_min + patch_size[2])
    
    # 提取结节区域
    nodule_patch = img_data[x_min:x_max, y_min:y_max, z_min:z_max]
    
    # 如果提取的区域小于指定大小，进行padding
    if nodule_patch.shape[0] < patch_size[0] or nodule_patch.shape[1] < patch_size[1] or nodule_patch.shape[2] < patch_size[2]:
        pad_x = max(0, patch_size[0] - nodule_patch.shape[0])
        pad_y = max(0, patch_size[1] - nodule_patch.shape[1])
        pad_z = max(0, patch_size[2] - nodule_patch.shape[2])
        
        nodule_patch = np.pad(
            nodule_patch, 
            ((0, pad_x), (0, pad_y), (0, pad_z)),
            mode='constant', 
            constant_values=0
        )
    
    # 确保大小一致
    nodule_patch = nodule_patch[:patch_size[0], :patch_size[1], :patch_size[2]]
    
    # 转换为张量并添加通道维度
    nodule_tensor = torch.from_numpy(nodule_patch).float().unsqueeze(0)
    
    return nodule_tensor


def preprocess_nodule(nodule_tensor, patch_size=[64, 64, 64]):
    """
    对提取的结节区域进行预处理
    
    Args:
        nodule_tensor: 提取的结节区域张量
        patch_size: 目标大小
    
    Returns:
        processed_tensor: 预处理后的张量
    """
    # 进行强度缩放
    min_val = nodule_tensor.min()
    max_val = nodule_tensor.max()
    
    # 如果图像值范围过小，可能是空区域，进行特殊处理
    if max_val - min_val < 1e-6:
        return torch.zeros((1, 1, *patch_size), dtype=torch.float32)  # 添加批次和通道维度
    
    # 缩放到[0,1]范围
    processed_tensor = (nodule_tensor - min_val) / (max_val - min_val)
    
    # 调整大小
    if nodule_tensor.shape[1:] != tuple(patch_size):
        # 创建一个空张量
        resized_tensor = torch.zeros((1, *patch_size), dtype=torch.float32)
        
        # 复制数据
        min_x = min(nodule_tensor.shape[1], patch_size[0])
        min_y = min(nodule_tensor.shape[2], patch_size[1])
        min_z = min(nodule_tensor.shape[3], patch_size[2])
        
        resized_tensor[0, :min_x, :min_y, :min_z] = processed_tensor[0, :min_x, :min_y, :min_z]
        processed_tensor = resized_tensor
    
    # 确保输出是5D张量 [B, C, D, H, W]
    if processed_tensor.dim() == 4:  # 如果是 [B, D, H, W]
        processed_tensor = processed_tensor.unsqueeze(1)  # 添加通道维度，变成 [B, C, D, H, W]
    
    return processed_tensor


def main():
    parser = argparse.ArgumentParser(description="肺结节分类系统")
    parser.add_argument("-c", "--classification-config", default="config/classification_config.json", help="分类配置文件路径")
    args = parser.parse_args()

    # 加载分类配置
    if not os.path.exists(args.classification_config):
        print(f"分类配置文件不存在: {args.classification_config}")
        return
    
    with open(args.classification_config, "r") as f:
        config = json.load(f)
    
    # 从配置中获取参数
    detection_env_file = config.get("detection_environment_file", "config/environment.json")
    results_file = config.get("detection_results_file", "ct_detection/output/results.json")
    classification_model_path = config.get("classification_model_path", "D:/lung_health_env/DukeLungRADS_CancerCL_Fold1_resnet50WSPP.pt")
    output_file = config.get("output_file", "output/classification_results.json")
    confidence_threshold = config.get("confidence_threshold", 0.5)
    model_config = config.get("model_config", {})
    
    # 加载检测环境配置
    if os.path.exists(detection_env_file):
        print(f"加载检测环境配置文件: {detection_env_file}")
        with open(detection_env_file, "r") as f:
            env_dict = json.load(f)
    else:
        print(f"检测环境配置文件不存在: {detection_env_file}，使用默认配置")
        env_dict = {}
    
    # 检查检测结果文件路径
    if not os.path.exists(results_file) and "result_list_file_path" in env_dict:
        # 如果指定的结果文件不存在，尝试使用环境配置中的路径
        results_file = env_dict["result_list_file_path"]
        print(f"使用环境配置中的结果文件路径: {results_file}")
    
    # 检查检测结果文件
    if not os.path.exists(results_file):
        print(f"检测结果文件不存在: {results_file}")
        return
    
    # 加载检测结果
    print(f"加载检测结果文件: {results_file}")
    with open(results_file, "r") as f:
        detection_results = json.load(f)
    
    # 加载分类模型
    print("加载分类模型...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 分类模型配置
    classification_config = {
        "Model_name": model_config.get("Model_name", "resnet50"),
        "spatial_dims": model_config.get("spatial_dims", 3),
        "n_input_channels": model_config.get("n_input_channels", 1),
        "num_classes": model_config.get("num_classes", 2),
        "image_key": model_config.get("image_key", "img"),
        "img_patch_size": model_config.get("img_patch_size", [64, 64, 64])
    }
    
    # 加载分类模型
    model = get_classification_model(
        Model_name=classification_config["Model_name"],
        spatial_dims=classification_config["spatial_dims"],
        n_input_channels=classification_config["n_input_channels"],
        num_classes=classification_config["num_classes"],
        device=device
    )
    
    # 加载模型权重
    try:
        # 尝试使用 weights_only=True 加载模型权重
        model.load_state_dict(torch.load(classification_model_path, map_location=device, weights_only=True))
        print("成功加载模型权重 (weights_only=True)")
    except Exception as e:
        print(f"使用 weights_only=True 加载模型权重失败: {e}")
        try:
            # 尝试使用默认方式加载模型权重
            model.load_state_dict(torch.load(classification_model_path, map_location=device))
            print("成功加载模型权重 (默认方式)")
        except Exception as e:
            print(f"加载模型权重失败: {e}")
            return
    
    model.eval()
    
    # 对每个检测到的结节进行分类
    print("对检测到的结节进行分类...")
    
    # 准备结果存储
    final_results = {"results": []}
    
    # 获取数据基础路径
    data_base_dir = env_dict.get("data_base_dir", "")
    
    # 遍历每个检测结果
    for result in detection_results["validation"]:
        image_path = result["image"]
        boxes = result["box"]
        scores = result["score"]
        labels = result["label"]
        
        print(f"处理图像: {image_path}")
        print(f"检测到 {len(boxes)} 个结节")
        
        image_results = {
            "image": image_path,
            "nodules": []
        }
        
        # 遍历每个检测到的结节
        for i, (box, score, label) in enumerate(zip(boxes, scores, labels)):
            print(f"  处理结节 {i+1}/{len(boxes)}")
            
            # 提取结节区域
            try:
                nodule_tensor = extract_nodule_patch(
                    image_path, 
                    box, 
                    patch_size=classification_config["img_patch_size"]
                )
                
                # 预处理结节
                processed_tensor = preprocess_nodule(
                    nodule_tensor, 
                    patch_size=classification_config["img_patch_size"]
                )
                
                # 打印张量形状，用于调试
                print(f"    处理后的张量形状: {processed_tensor.shape}")
                
                # 确保张量是5D的 [B, C, D, H, W]
                if processed_tensor.dim() != 5:
                    print(f"    警告: 张量维度不正确，当前为 {processed_tensor.dim()}D，需要 5D")
                    if processed_tensor.dim() == 4:  # [B, D, H, W]
                        processed_tensor = processed_tensor.unsqueeze(1)  # 添加通道维度
                        print(f"    已添加通道维度，现在形状为: {processed_tensor.shape}")
                
                # 进行分类
                with torch.no_grad():
                    processed_tensor = processed_tensor.to(device)
                    classification_output = model(processed_tensor)
                    softmax = torch.nn.Softmax(dim=1)
                    probabilities = softmax(classification_output)
                    malignancy_prob = probabilities[0, 1].item()  # 恶性概率
                
                # 确定分类结果
                is_malignant = malignancy_prob > confidence_threshold
                
                # 存储结果
                nodule_result = {
                    "box": box,
                    "detection_score": score,
                    "detection_label": label,
                    "malignancy_probability": malignancy_prob,
                    "is_malignant": is_malignant
                }
                
                image_results["nodules"].append(nodule_result)
                
                print(f"    结节位置: [{box[0]:.1f}, {box[1]:.1f}, {box[2]:.1f}, {box[3]:.1f}, {box[4]:.1f}, {box[5]:.1f}]")
                print(f"    检测置信度: {score:.4f}")
                print(f"    恶性概率: {malignancy_prob:.4f}")
                print(f"    分类结果: {'恶性' if is_malignant else '良性'}")
                
            except Exception as e:
                print(f"    处理结节时出错: {str(e)}")
                print(f"    错误详情: {type(e).__name__}: {str(e)}")
                import traceback
                traceback.print_exc()
        
        final_results["results"].append(image_results)
    
    # 保存最终结果
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, "w") as f:
        json.dump(final_results, f, indent=2)
    
    print(f"分类完成，结果已保存至: {output_file}")


if __name__ == "__main__":
    main()