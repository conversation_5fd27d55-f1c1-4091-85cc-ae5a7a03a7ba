---
description: good
globs: 
alwaysApply: false
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
这个代码仓库包含了计算机视觉项目，包含了模型的检测和训练，包含了肺结节发识别任务和分类任务。还有一个是医学影像AI常用库，包含了医学影像AI常用的库。还有肺结节的分割以及影像组学参数提取计算。

## 项目1：肺结节发识别任务
这个项目包含了肺结节发识别任务的模型的检测和训练。

## 项目2：分类任务
这个项目包含了分类任务的模型的检测和训练。

## 项目3：医学影像AI常用库
这个项目包含了医学影像AI常用的库。
## 项目4：肺结节的分割
这个项目包含了肺结节的分割。

## 项目5：影像组学参数提取计算
这个项目包含了影像组学参数提取计算。
减少代码的冗余，提高代码的可读性，提高代码的可维护性，提高代码的可扩展性。
不要添加不必要的依赖，不要添加不必要的代码，不要添加不必要的注释，不要添加不必要的变量，不要添加不必要的函数，不要添加不必要的类，不要添加不必要的模块，不要添加不必要的文件，不要添加不必要的文件夹。
python==3.10.16
pip==25.1
setuptools==78.1.1
wheel==0.45.1
numpy==1.26.4
scipy==1.15.3
pandas==2.1.0
matplotlib==3.10.3
scikit-image==0.25.2
sympy==1.13.1
tqdm==4.67.1
seaborn
pytorch==2.5.1
pytorch-cuda==12.1
torchvision==0.20.1
torchaudio==2.5.1
monai==1.4.0     # 医学影像AI常用库
cuda-version==12.9
cuda-runtime==12.1.0
cuda-cudart==12.1.105
libcublas==*********
libcufft==********
libcurand==**********
libcusolver==*********
libcusparse==*********
libnvjpeg==*********
pydicom==2.4.3
simpleitk==2.3.0
itk==5.4.3
nibabel==5.1.0
imageio==2.37.0
tifffile==2025.5.10
tensorboard==2.15.1
tensorboard-data-server==0.7.2
protobuf==3.20.3
requests==2.32.3
urllib3==2.3.0
httpx==0.28.1
pyyaml==6.0.2
typing_extensions==4.12.2
filelock==3.17.0
packaging==25.0
jsonschema==4.23.0
ipykernel==6.29.5
ipython==8.36.0
jupyter-client==8.6.3
jupyter-core==5.7.2
jupyterlab==4.4.2
notebook==7.4.2
notebook-shim==0.2.4
jupyterlab-server==2.27.3
jupyter-server==2.16.0
jupyter-server-terminals==0.5.3
nbconvert==7.16.6
nbformat==5.10.4


另外一个项目的环境依赖：
# 影像组学特征提取项目依赖包
# 在独立的虚拟环境中安装
# 影像组学核心库
pyradiomics==3.0.1
# 医学图像处理
nibabel==4.0.2
SimpleITK==2.2.1
# 数据处理和科学计算
numpy==1.24.3
scipy==1.10.1
pandas==2.0.3
scikit-image==0.21.0
scikit-learn==1.3.0
# 可视化
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.15.0
# 配置文件处理
PyYAML==6.0.1
# 日志和进度条
tqdm==4.65.0
loguru==0.7.0
# 数据验证
pydantic==2.1.1
# 并行处理
joblib==1.3.1
# 开发和测试
pytest==7.4.0
pytest-cov==4.1.0
black==23.7.0
flake8==6.0.0
# 其他工具
openpyxl==3.1.2
xlsxwriter==3.1.2