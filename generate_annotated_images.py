import json
import os
import argparse
import numpy as np
import nibabel as nib
import pydicom
from pydicom.dataset import FileDataset, FileMetaDataset
from pydicom.uid import generate_uid
import SimpleITK as sitk
from datetime import datetime
from tqdm import tqdm
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys

def load_json_predictions(json_file):
    """
    加载包含预测结果的JSON文件
    """
    with open(json_file, 'r') as f:
        predictions = json.load(f)
    return predictions

def load_config(config_file):
    """
    加载配置文件
    """
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"加载配置文件 {config_file} 时出错: {e}")
        return None

def load_nifti_image(file_path):
    """
    加载NIfTI格式的影像数据
    """
    try:
        img = nib.load(file_path)
        data = img.get_fdata()
        affine = img.affine
        header = img.header
        print(f"已加载NIfTI影像: {file_path}, 形状: {data.shape}")
        return data, affine, header
    except Exception as e:
        print(f"加载影像 {file_path} 时出错: {e}")
        return None, None, None

def load_dicom_series(directory):
    """
    加载DICOM系列影像数据
    """
    try:
        reader = sitk.ImageSeriesReader()
        dicom_names = reader.GetGDCMSeriesFileNames(directory)
        reader.SetFileNames(dicom_names)
        image = reader.Execute()
        array = sitk.GetArrayFromImage(image)
        print(f"已加载DICOM系列: {directory}, 形状: {array.shape}")
        return array, image
    except Exception as e:
        print(f"加载DICOM系列 {directory} 时出错: {e}")
        return None, None

def add_bounding_boxes_to_image(
    image_data: np.ndarray, 
    boxes: list, 
    scores: list, 
    threshold: float = 0.5, 
    color_value: int = 255, 
    affine: np.ndarray = None, 
    fill_box: bool = False, 
    fill_value: int = 100
) -> np.ndarray:
    """
    将边界框添加到影像数据中
    
    Args:
        image_data: 3D影像数据数组
        boxes: 边界框列表，格式为[x1, y1, z1, x2, y2, z2]
        scores: 置信度分数列表
        threshold: 置信度阈值，低于此值的预测将被忽略
        color_value: 边界框的像素值
        affine: 原始影像的仿射矩阵（当前未使用，保留用于未来扩展）
        fill_box: 是否填充边界框内部
        fill_value: 填充的像素值
        
    Returns:
        添加了边界框的影像数据
    """
    # 创建一个新的数组来存储带有边界框的影像数据
    annotated_data = image_data.copy()
    
    # 获取影像数据的维度
    dim_x, dim_y, dim_z = image_data.shape
    print(f"影像数据维度: {image_data.shape}")
    
    # 处理边界框
    box_count = 0
    thickness = 2  # 边界框厚度
    
    for box, score in zip(boxes, scores):
        if score < threshold:
            continue
            
        box_count += 1
        # 提取并限制边界框坐标
        x1, y1, z1, x2, y2, z2 = [int(coord) for coord in box]
        
        print(f"处理边界框 #{box_count}: 原始坐标: ({x1}, {y1}, {z1}) 到 ({x2}, {y2}, {z2}), 置信度: {score}")
        
        # 确保坐标在有效范围内
        x1, x2 = max(0, min(x1, dim_x-1)), max(0, min(x2, dim_x-1))
        y1, y2 = max(0, min(y1, dim_y-1)), max(0, min(y2, dim_y-1))
        z1, z2 = max(0, min(z1, dim_z-1)), max(0, min(z2, dim_z-1))
        
        # 确保x1 < x2, y1 < y2, z1 < z2
        if x1 > x2: x1, x2 = x2, x1
        if y1 > y2: y1, y2 = y2, y1
        if z1 > z2: z1, z2 = z2, z1
        
        print(f"边界框 #{box_count}: 范围限制后坐标: ({x1}, {y1}, {z1}) 到 ({x2}, {y2}, {z2})")
        
        # 确保边界框有一定的大小
        min_size = 5
        x2 = max(x2, min(x1 + min_size, dim_x - 1))
        y2 = max(y2, min(y1 + min_size, dim_y - 1))
        z2 = max(z2, min(z1 + min_size, dim_z - 1))
                
        print(f"边界框 #{box_count}: 确保最小尺寸后坐标: ({x1}, {y1}, {z1}) 到 ({x2}, {y2}, {z2})")
        
        # 如果需要填充边界框内部
        if fill_box:
            print(f"填充边界框 #{box_count} 内部，填充值: {fill_value}")
            for x in range(x1, x2+1):
                for y in range(y1, y2+1):
                    annotated_data[x, y, z1:z2+1] = fill_value
        
        # 绘制边界框的六个面
        # X方向的边
        for x in range(x1, x2+1):
            for t in range(thickness):
                # 前面和后面
                annotated_data[x, max(0, y1 - t), z1:z2+1] = color_value
                annotated_data[x, min(dim_y - 1, y2 + t), z1:z2+1] = color_value
        
        # Y方向的边
        for y in range(y1, y2+1):
            for t in range(thickness):
                # 左面和右面
                annotated_data[max(0, x1 - t), y, z1:z2+1] = color_value
                annotated_data[min(dim_x - 1, x2 + t), y, z1:z2+1] = color_value
        
        # Z方向的边
        for z in range(z1, z2+1):
            for t in range(thickness):
                # 上面和下面
                annotated_data[x1:x2+1, max(0, y1 - t), z] = color_value
                annotated_data[x1:x2+1, min(dim_y - 1, y2 + t), z] = color_value
        
        print(f"已绘制边界框 #{box_count}")
    
    print(f"总共处理了 {box_count} 个边界框")
    return annotated_data

def save_as_nifti(
    data: np.ndarray, 
    affine: np.ndarray, 
    header: object, 
    output_file: str
) -> bool:
    """
    将影像数据保存为NIfTI格式，并始终执行Z轴翻转以解决S-I方向颠倒问题
    
    Args:
        data: 3D影像数据
        affine: 仿射矩阵
        header: NIfTI头信息
        output_file: 输出文件路径
        
    Returns:
        bool: 处理是否成功
    """
    try:
        # 沿Z轴翻转数据以解决S-I方向颠倒问题
        flipped_data = data[:, :, ::-1]
        print(f"已沿Z轴翻转数据以解决S-I方向颠倒问题")
        
        # 创建NIfTI对象并保存
        new_img = nib.Nifti1Image(flipped_data, affine, header)
        nib.save(new_img, output_file)
        
        print(f"已保存NIfTI文件: {output_file}")
        return True
    except Exception as e:
        print(f"保存NIfTI文件 {output_file} 时出错: {e}")
        return False

def save_as_dicom(data, original_dicom_dir, output_dir):
    """
    将影像数据保存为DICOM系列
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取原始DICOM文件列表
        dicom_files = []
        for root, _, files in os.walk(original_dicom_dir):
            for file in files:
                if file.endswith('.dcm'):
                    dicom_files.append(os.path.join(root, file))
        
        # 按照实例编号排序DICOM文件
        dicom_files.sort()
        
        # 检查切片数量是否匹配
        if len(dicom_files) != data.shape[2]:
            print(f"警告: DICOM文件数量 ({len(dicom_files)}) 与影像数据切片数量 ({data.shape[2]}) 不匹配")
            print(f"数据形状: {data.shape}")
            return False
        
        # 为每个切片创建新的DICOM文件
        for i, dicom_file in enumerate(tqdm(dicom_files, desc="保存DICOM文件")):
            # 读取原始DICOM文件
            ds = pydicom.dcmread(dicom_file)
            
            # 创建新的DICOM数据集
            new_ds = pydicom.Dataset()
            
            # 复制原始DICOM的元数据
            for elem in ds:
                if elem.tag != pydicom.tag.Tag(0x7fe0, 0x0010):  # 排除像素数据
                    new_ds.add(elem)
            
            # 更新SOP实例UID
            new_ds.SOPInstanceUID = generate_uid()
            
            # 更新序列描述，添加标记信息
            if hasattr(new_ds, 'SeriesDescription'):
                new_ds.SeriesDescription = new_ds.SeriesDescription + "_with_predictions"
            else:
                new_ds.SeriesDescription = "With_predictions"
            
            # 更新序列UID
            new_ds.SeriesInstanceUID = generate_uid()
            
            # 设置新的像素数据
            # 注意: data是经过转置的，所以我们使用[:, :, i]而不是[i, :, :]
            new_ds.PixelData = data[:, :, i].astype(np.uint16).tobytes()
            
            # 保存新的DICOM文件
            output_file = os.path.join(output_dir, f"slice_{i:04d}.dcm")
            new_ds.save_as(output_file)
        
        print(f"已保存DICOM系列到: {output_dir}")
        return True
    except Exception as e:
        print(f"保存DICOM系列到 {output_dir} 时出错: {e}")
        return False

def process_image_with_options(
    image_path: str, 
    boxes: list, 
    scores: list, 
    output_format: str, 
    output_dir: str, 
    threshold: float = 0.5, 
    color_value: int = 255, 
    fill_box: bool = False, 
    fill_value: int = 100, 
    z_offset: int = 0
) -> bool:
    """
    处理单个影像文件，对边界框坐标进行优化转换并添加到影像中
    
    Args:
        image_path: 影像文件路径
        boxes: 边界框列表
        scores: 置信度分数列表
        output_format: 输出格式，"nifti"或"dicom"
        output_dir: 输出目录
        threshold: 置信度阈值
        color_value: 边界框颜色值
        fill_box: 是否填充边界框
        fill_value: 填充值
        z_offset: Z轴偏移量，默认为0
        
    Returns:
        bool: 处理是否成功
    """
    # 确定输入是NIfTI文件还是DICOM目录
    is_nifti = image_path.lower().endswith(('.nii', '.nii.gz'))
    
    if is_nifti:
        return _process_nifti_image(
            image_path, boxes, scores, output_format, output_dir,
            threshold, color_value, fill_box, fill_value, z_offset
        )
    else:
        return _process_dicom_image(
            image_path, boxes, scores, output_format, output_dir,
            threshold, color_value, fill_box, fill_value, z_offset
        )

def _process_nifti_image(
    image_path: str, 
    boxes: list, 
    scores: list, 
    output_format: str, 
    output_dir: str,
    threshold: float = 0.5, 
    color_value: int = 255, 
    fill_box: bool = False, 
    fill_value: int = 100, 
    z_offset: int = 0
) -> bool:
    """处理NIfTI格式的影像文件"""
    # 加载NIfTI影像
    image_data, affine, header = load_nifti_image(image_path)
    if image_data is None:
        return False
        
    print(f"处理NIfTI影像: {image_path}")
    print(f"影像形状: {image_data.shape}")
    print(f"仿射矩阵: \n{affine}")
    
    print(f"原始边界框: {boxes}")
    
    # 调整边界框坐标
    adjusted_boxes = _adjust_nifti_boxes(boxes, image_data.shape, z_offset)
    
    print(f"调整后的边界框: {adjusted_boxes}")
    print(f"使用优化的坐标映射方法: 翻转x和y坐标，并将z坐标增加约{z_offset}个单位")
        
    # 添加边界框
    annotated_data = add_bounding_boxes_to_image(
        image_data, 
        adjusted_boxes, 
        scores, 
        threshold, 
        color_value, 
        None,  # 不使用仿射矩阵
        fill_box=fill_box,
        fill_value=fill_value
    )
    
    # 保存结果
    base_name = os.path.basename(image_path)
    output_file = os.path.join(output_dir, f"{os.path.splitext(base_name)[0]}_annotated.nii.gz")
    
    if output_format != "nifti":
        print("警告: 无法将NIfTI直接转换为DICOM系列，需要原始DICOM数据")
        print("将以NIfTI格式保存结果")
        
    return save_as_nifti(annotated_data, affine, header, output_file)

def _process_dicom_image(
    image_path: str, 
    boxes: list, 
    scores: list, 
    output_format: str, 
    output_dir: str,
    threshold: float = 0.5, 
    color_value: int = 255, 
    fill_box: bool = False, 
    fill_value: int = 100, 
    z_offset: int = 0
) -> bool:
    """处理DICOM格式的影像文件"""
    # 加载DICOM系列
    dicom_dir = os.path.dirname(image_path)
    image_data, original_image = load_dicom_series(dicom_dir)
    if image_data is None:
        return False
        
    print(f"处理DICOM系列: {dicom_dir}")
    print(f"DICOM影像形状: {image_data.shape}")
    
    # 创建DICOM的仿射矩阵
    dicom_affine = _create_dicom_affine(original_image)
    
    # 转置数据以适应处理逻辑
    transposed_data = np.transpose(image_data, (2, 1, 0))
    print(f"转置后的DICOM数据形状: {transposed_data.shape}")
    
    # 调整边界框坐标
    adjusted_boxes = _adjust_dicom_boxes(boxes, image_data.shape, z_offset)
    
    print(f"调整前的边界框: {boxes}")
    print(f"调整后的边界框: {adjusted_boxes}")
    print(f"使用优化的坐标映射方法: 翻转x和y坐标，并将z坐标增加约{z_offset}个单位")
        
    # 添加边界框
    annotated_data = add_bounding_boxes_to_image(
        transposed_data, 
        adjusted_boxes, 
        scores, 
        threshold, 
        color_value, 
        None,  # 不使用仿射矩阵
        fill_box=fill_box,
        fill_value=fill_value
    )
    
    # 保存结果
    if output_format == "nifti":
        # 如果要求NIfTI输出但输入是DICOM
        output_file = os.path.join(output_dir, f"{os.path.basename(dicom_dir)}_annotated.nii.gz")
        return save_as_nifti(annotated_data, dicom_affine, None, output_file)
    else:
        # 创建输出目录
        dicom_output_dir = os.path.join(output_dir, f"{os.path.basename(dicom_dir)}_annotated")
        return save_as_dicom(annotated_data, dicom_dir, dicom_output_dir)

def _adjust_nifti_boxes(boxes: list, shape: tuple, z_offset: int) -> list:
    """
    调整NIfTI数据的边界框坐标
    
    Args:
        boxes: 原始边界框坐标
        shape: 影像数据形状 (x_dim, y_dim, z_dim)
        z_offset: Z轴偏移量
        
    Returns:
        调整后的边界框坐标
    """
    adjusted_boxes = []
    x_dim, y_dim, z_dim = shape
    
    for box in boxes:
        x1, y1, z1, x2, y2, z2 = box
        
        # 翻转x和y坐标
        x1_adj = x_dim - x1 - 1
        x2_adj = x_dim - x2 - 1
        y1_adj = y_dim - y1 - 1
        y2_adj = y_dim - y2 - 1
        
        # 调整z坐标
        z1_adj = z1 + z_offset
        z2_adj = z2 + z_offset
        
        # 确保坐标在有效范围内
        z1_adj = max(0, min(z1_adj, z_dim-1))
        z2_adj = max(0, min(z2_adj, z_dim-1))
        
        # 确保坐标顺序正确
        if x1_adj > x2_adj:
            x1_adj, x2_adj = x2_adj, x1_adj
        if y1_adj > y2_adj:
            y1_adj, y2_adj = y2_adj, y1_adj
        if z1_adj > z2_adj:
            z1_adj, z2_adj = z2_adj, z1_adj
            
        adjusted_box = [x1_adj, y1_adj, z1_adj, x2_adj, y2_adj, z2_adj]
        
        # 确保所有坐标在有效范围内
        adjusted_box[0] = max(0, min(adjusted_box[0], x_dim-1))  # x坐标
        adjusted_box[3] = max(0, min(adjusted_box[3], x_dim-1))
        adjusted_box[1] = max(0, min(adjusted_box[1], y_dim-1))  # y坐标
        adjusted_box[4] = max(0, min(adjusted_box[4], y_dim-1))
        adjusted_box[2] = max(0, min(adjusted_box[2], z_dim-1))  # z坐标
        adjusted_box[5] = max(0, min(adjusted_box[5], z_dim-1))
        
        adjusted_boxes.append(adjusted_box)
        
    return adjusted_boxes

def _adjust_dicom_boxes(boxes: list, shape: tuple, z_offset: int) -> list:
    """
    调整DICOM数据的边界框坐标
    
    Args:
        boxes: 原始边界框坐标
        shape: 影像数据形状 (z_dim, y_dim, x_dim)
        z_offset: Z轴偏移量
        
    Returns:
        调整后的边界框坐标
    """
    adjusted_boxes = []
    z_dim, y_dim, x_dim = shape  # DICOM的维度顺序
    
    for box in boxes:
        x1, y1, z1, x2, y2, z2 = box
        
        # 翻转x和y坐标
        x1_adj = x_dim - x1 - 1
        x2_adj = x_dim - x2 - 1
        y1_adj = y_dim - y1 - 1
        y2_adj = y_dim - y2 - 1
        
        # 调整z坐标
        z1_adj = z1 + z_offset
        z2_adj = z2 + z_offset
        
        # 确保坐标在有效范围内
        z1_adj = max(0, min(z1_adj, z_dim-1))
        z2_adj = max(0, min(z2_adj, z_dim-1))
        
        # 确保坐标顺序正确
        if z1_adj > z2_adj:
            z1_adj, z2_adj = z2_adj, z1_adj
        if y1_adj > y2_adj:
            y1_adj, y2_adj = y2_adj, y1_adj
        if x1_adj > x2_adj:
            x1_adj, x2_adj = x2_adj, x1_adj
            
        # 交换x和z坐标以适应DICOM的维度顺序
        adjusted_box = [z1_adj, y1_adj, x1_adj, z2_adj, y2_adj, x2_adj]
        
        # 确保坐标在有效范围内
        adjusted_box[0] = max(0, min(adjusted_box[0], z_dim-1))  # z坐标
        adjusted_box[3] = max(0, min(adjusted_box[3], z_dim-1))
        adjusted_box[1] = max(0, min(adjusted_box[1], y_dim-1))  # y坐标
        adjusted_box[4] = max(0, min(adjusted_box[4], y_dim-1))
        adjusted_box[2] = max(0, min(adjusted_box[2], x_dim-1))  # x坐标
        adjusted_box[5] = max(0, min(adjusted_box[5], x_dim-1))
        
        adjusted_boxes.append(adjusted_box)
        
    return adjusted_boxes

def _create_dicom_affine(original_image):
    """从SimpleITK图像创建仿射矩阵"""
    dicom_affine = np.eye(4)
    if original_image is not None:
        # 从SimpleITK图像获取间距和方向
        spacing = original_image.GetSpacing()
        direction = original_image.GetDirection()
        origin = original_image.GetOrigin()
        
        print(f"DICOM间距: {spacing}")
        print(f"DICOM方向: {direction}")
        print(f"DICOM原点: {origin}")
        
        # 设置仿射矩阵
        for i in range(3):
            for j in range(3):
                dicom_affine[i, j] = direction[i*3 + j] * spacing[j]
        
        # 设置原点
        dicom_affine[0:3, 3] = origin
        
        print(f"构建的DICOM仿射矩阵: \n{dicom_affine}")
    
    return dicom_affine

class AnnotationGUI:
    """医学影像预测结果可视化工具的图形界面"""
    
    def __init__(self, root: tk.Tk):
        """初始化GUI界面
        
        Args:
            root: Tkinter根窗口
        """
        self.root = root
        self.root.title("医学影像预测结果可视化工具")
        self.root.geometry("600x550")  # 调整高度
        
        # 设置样式
        self._setup_styles()
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建输入控件
        self._create_input_widgets(main_frame)
        
        # 创建日志区域
        self._create_log_area(main_frame)
        
        # 设置列和行的权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(11, weight=1)
        
        # 重定向标准输出到日志框
        self.stdout = sys.stdout
        sys.stdout = self
    
    def _setup_styles(self):
        """设置控件样式"""
        style = ttk.Style()
        style.configure("TButton", padding=6, relief="flat", background="#ccc")
        style.configure("TLabel", padding=6, font=('Helvetica', 10))
        style.configure("TFrame", padding=10)
    
    def _create_input_widgets(self, parent: ttk.Frame):
        """创建输入控件
        
        Args:
            parent: 父容器
        """
        # 预测结果JSON文件
        row = 0
        ttk.Label(parent, text="预测结果JSON文件:").grid(column=0, row=row, sticky=tk.W)
        self.predictions_path = tk.StringVar(value="D:/Code/AI-in-Lung-Detection/ct_detection/output/results.json")
        ttk.Entry(parent, width=50, textvariable=self.predictions_path).grid(column=1, row=row, sticky=(tk.W, tk.E))
        ttk.Button(parent, text="浏览...", command=lambda: self._browse_file(self.predictions_path, "选择预测结果JSON文件", [("JSON文件", "*.json"), ("所有文件", "*.*")])).grid(column=2, row=row, sticky=tk.W)
        
        # 配置文件
        row += 1
        ttk.Label(parent, text="配置文件:").grid(column=0, row=row, sticky=tk.W)
        self.config_path = tk.StringVar(value="D:/Code/AI-in-Lung-Detection/config/config_test.json")
        ttk.Entry(parent, width=50, textvariable=self.config_path).grid(column=1, row=row, sticky=(tk.W, tk.E))
        ttk.Button(parent, text="浏览...", command=lambda: self._browse_file(self.config_path, "选择配置文件", [("JSON文件", "*.json"), ("所有文件", "*.*")])).grid(column=2, row=row, sticky=tk.W)
        
        # 输出目录
        row += 1
        ttk.Label(parent, text="输出目录:").grid(column=0, row=row, sticky=tk.W)
        self.output_dir = tk.StringVar(value="./output/annotated_images")
        ttk.Entry(parent, width=50, textvariable=self.output_dir).grid(column=1, row=row, sticky=(tk.W, tk.E))
        ttk.Button(parent, text="浏览...", command=self._browse_output_dir).grid(column=2, row=row, sticky=tk.W)
        
        # 输出格式
        row += 1
        ttk.Label(parent, text="输出格式:").grid(column=0, row=row, sticky=tk.W)
        self.output_format = tk.StringVar(value="nifti")
        format_frame = ttk.Frame(parent)
        format_frame.grid(column=1, row=row, sticky=tk.W)
        ttk.Radiobutton(format_frame, text="NIfTI", variable=self.output_format, value="nifti").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(format_frame, text="DICOM", variable=self.output_format, value="dicom").pack(side=tk.LEFT, padx=5)
        
        # 置信度阈值
        row += 1
        ttk.Label(parent, text="置信度阈值:").grid(column=0, row=row, sticky=tk.W)
        self.threshold = tk.DoubleVar(value=0.5)
        threshold_scale = ttk.Scale(parent, from_=0.0, to=1.0, variable=self.threshold, orient=tk.HORIZONTAL)
        threshold_scale.grid(column=1, row=row, sticky=(tk.W, tk.E))
        ttk.Label(parent, textvariable=self.threshold).grid(column=2, row=row, sticky=tk.W)
        
        # 边界框颜色值
        row += 1
        ttk.Label(parent, text="边界框颜色值:").grid(column=0, row=row, sticky=tk.W)
        self.color_value = tk.IntVar(value=255)
        ttk.Entry(parent, width=10, textvariable=self.color_value).grid(column=1, row=row, sticky=tk.W)
        
        # 填充边界框选项
        row += 1
        ttk.Label(parent, text="填充边界框:").grid(column=0, row=row, sticky=tk.W)
        self.fill_box = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="启用填充", variable=self.fill_box).grid(column=1, row=row, sticky=tk.W)
        
        # 填充值
        row += 1
        ttk.Label(parent, text="填充值:").grid(column=0, row=row, sticky=tk.W)
        self.fill_value = tk.IntVar(value=100)
        ttk.Entry(parent, width=10, textvariable=self.fill_value).grid(column=1, row=row, sticky=tk.W)
        
        # Z轴偏移量
        row += 1
        ttk.Label(parent, text="Z轴偏移量:").grid(column=0, row=row, sticky=tk.W)
        self.z_offset = tk.IntVar(value=0)
        ttk.Entry(parent, width=10, textvariable=self.z_offset).grid(column=1, row=row, sticky=tk.W)
        ttk.Label(parent, text="(默认值0，根据需要调整)").grid(column=2, row=row, sticky=tk.W)
        
        # 处理按钮
        row += 1
        ttk.Button(parent, text="开始处理", command=self.process).grid(column=1, row=row, sticky=tk.E, pady=10)
    
    def _create_log_area(self, parent: ttk.Frame):
        """创建日志区域
        
        Args:
            parent: 父容器
        """
        row = 10
        ttk.Label(parent, text="处理日志:").grid(column=0, row=row, sticky=tk.NW)
        
        row += 1
        self.log_text = tk.Text(parent, wrap=tk.WORD, width=60, height=10)
        self.log_text.grid(column=0, row=row, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.grid(column=3, row=row, sticky=(tk.N, tk.S))
        self.log_text['yscrollcommand'] = scrollbar.set
    
    def _browse_file(self, var: tk.StringVar, title: str, filetypes: list):
        """浏览文件对话框
        
        Args:
            var: 存储结果的变量
            title: 对话框标题
            filetypes: 文件类型过滤
        """
        filename = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if filename:
            var.set(filename)
    
    def _browse_output_dir(self):
        """浏览输出目录对话框"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)
    
    def write(self, text: str):
        """写入日志文本（用于重定向stdout）"""
        self.log_text.insert(tk.END, text)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def flush(self):
        """刷新缓冲区（用于重定向stdout）"""
        pass
    
    def process(self):
        """处理按钮点击事件，开始处理影像"""
        # 清空日志
        self.log_text.delete(1.0, tk.END)
        
        try:
            # 获取参数
            params = self._get_processing_params()
            
            # 确保输出目录存在
            os.makedirs(params["output_dir"], exist_ok=True)
            
            # 加载预测结果
            print(f"加载预测结果: {params['predictions_path']}")
            predictions = load_json_predictions(params["predictions_path"])
            validation_predictions = predictions.get("validation", [])
            
            if not validation_predictions:
                print("预测结果为空，请检查JSON文件")
                return
            
            # 加载配置文件
            print(f"加载配置文件: {params['config_path']}")
            config = load_config(params["config_path"])
            
            if not config:
                print("配置文件加载失败")
                return
            
            # 处理预测结果
            _process_predictions(
                validation_predictions,
                config,
                params["output_format"],
                params["output_dir"],
                params["threshold"],
                params["color_value"],
                params["fill_box"],
                params["fill_value"],
                params["z_offset"]
            )
            
            # 显示完成消息
            messagebox.showinfo("完成", "所有图像处理完成!")
            
        except Exception as e:
            print(f"处理过程中出错: {str(e)}")
            messagebox.showerror("错误", f"处理过程中出错: {str(e)}")
    
    def _get_processing_params(self) -> dict:
        """获取所有处理参数
        
        Returns:
            包含所有处理参数的字典
        """
        return {
            "predictions_path": self.predictions_path.get(),
            "config_path": self.config_path.get(),
            "output_dir": self.output_dir.get(),
            "output_format": self.output_format.get(),
            "threshold": self.threshold.get(),
            "color_value": self.color_value.get(),
            "fill_box": self.fill_box.get(),
            "fill_value": self.fill_value.get(),
            "z_offset": self.z_offset.get()
        }

def process_image(
    image_path: str, 
    boxes: list, 
    scores: list, 
    output_format: str, 
    output_dir: str, 
    threshold: float = 0.5, 
    color_value: int = 255
) -> bool:
    """
    处理单个影像文件（命令行模式的简化版本）
    
    这是process_image_with_options函数的简化版本，使用默认参数：
    - fill_box=False：不填充边界框内部
    - fill_value=100：如果启用填充，使用100作为填充值
    - z_offset=0：Z轴偏移量为0
    
    Args:
        image_path: 影像文件路径
        boxes: 边界框列表
        scores: 置信度分数列表
        output_format: 输出格式，"nifti"或"dicom"
        output_dir: 输出目录
        threshold: 置信度阈值
        color_value: 边界框颜色值
    
    Returns:
        bool: 处理是否成功
    """
    return process_image_with_options(
        image_path, 
        boxes, 
        scores, 
        output_format, 
        output_dir, 
        threshold, 
        color_value, 
        fill_box=False, 
        fill_value=100,
        z_offset=0
    )

def main():
    """主函数：处理命令行参数或启动GUI界面"""
    # 检查是否有命令行参数
    if len(os.sys.argv) > 1:
        _run_command_line_mode()
    else:
        _run_gui_mode()

def _run_command_line_mode():
    """运行命令行模式"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="将预测结果边界框添加到原始影像数据中")
    parser.add_argument("--predictions", type=str, required=True, help="包含预测结果的JSON文件路径")
    parser.add_argument("--config", type=str, default="../config/config_test.json", help="配置文件路径")
    parser.add_argument("--output", type=str, default="./output/annotated_images", help="输出文件或目录路径")
    parser.add_argument("--format", type=str, choices=["nifti", "dicom"], default="nifti", help="输出格式: nifti 或 dicom")
    parser.add_argument("--threshold", type=float, default=0.5, help="预测置信度阈值")
    parser.add_argument("--color", type=int, default=255, help="边界框颜色值")
    parser.add_argument("--z-offset", type=int, default=0, help="Z轴偏移量，默认为0")
    parser.add_argument("--fill-box", action="store_true", help="是否填充边界框内部")
    parser.add_argument("--fill-value", type=int, default=100, help="边界框填充值，默认为100")
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output, exist_ok=True)
    
    # 加载预测结果和配置
    try:
        predictions = load_json_predictions(args.predictions)
        validation_predictions = predictions.get("validation", [])
        
        if not validation_predictions:
            print("预测结果为空，退出程序")
            return
            
        config = load_config(args.config)
        
        # 处理每个预测结果
        _process_predictions(
            validation_predictions, 
            config, 
            args.format, 
            args.output, 
            args.threshold, 
            args.color, 
            args.fill_box, 
            args.fill_value, 
            args.z_offset
        )
    except Exception as e:
        print(f"处理过程中出错: {e}")
        sys.exit(1)

def _run_gui_mode():
    """运行GUI模式"""
    root = tk.Tk()
    app = AnnotationGUI(root)
    root.mainloop()

def _process_predictions(
    predictions: list, 
    config: dict, 
    output_format: str, 
    output_dir: str, 
    threshold: float, 
    color_value: int, 
    fill_box: bool, 
    fill_value: int, 
    z_offset: int
):
    """处理预测结果列表"""
    # 如果配置文件有效，使用配置中的图像路径
    if config and "data_base_dir" in config and "validation" in config:
        validation_images = config["validation"]
        
        for pred in predictions:
            # 查找配置文件中对应的原始影像路径
            original_image_path = _find_original_image_path(pred["image"], validation_images)
            
            print(f"处理图像: {original_image_path}")
            success = process_image_with_options(
                original_image_path,
                pred["box"],
                pred["score"],
                output_format,
                output_dir,
                threshold,
                color_value,
                fill_box,
                fill_value,
                z_offset
            )
            
            _report_processing_result(success, original_image_path)
    else:
        # 直接使用预测结果中的路径
        for pred in predictions:
            image_path = pred["image"]
            print(f"处理图像: {image_path}")
            
            success = process_image_with_options(
                image_path,
                pred["box"],
                pred["score"],
                output_format,
                output_dir,
                threshold,
                color_value,
                fill_box,
                fill_value,
                z_offset
            )
            
            _report_processing_result(success, image_path)
    
    print("所有处理完成!")

def _find_original_image_path(pred_image_path: str, validation_images: list) -> str:
    """在配置文件中查找原始影像路径"""
    for img_info in validation_images:
        if "image" in img_info and os.path.basename(img_info["image"]) == os.path.basename(pred_image_path):
            return img_info["image"]
    
    # 如果在配置文件中找不到，则使用预测结果中的路径
    return pred_image_path

def _report_processing_result(success: bool, image_path: str):
    """报告处理结果"""
    if success:
        print(f"成功处理图像: {image_path}")
    else:
        print(f"处理图像失败: {image_path}")

if __name__ == "__main__":
    main() 