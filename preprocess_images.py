#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CT图像预处理脚本
用于检测任务前的图像标准化处理

主要功能：
1. 重采样到标准分辨率
2. 强度归一化
3. 方向标准化
4. 格式转换

Author: AI Assistant
Date: 2025-01-31
"""

import os
import argparse
import nibabel as nib
import numpy as np
from pathlib import Path
import SimpleITK as sitk

def resample_image(image, target_spacing=[0.703125, 0.703125, 1.25]):
    """
    重采样图像到目标分辨率
    
    Args:
        image: SimpleITK图像对象
        target_spacing: 目标体素间距 [x, y, z]
        
    Returns:
        重采样后的图像
    """
    original_spacing = image.GetSpacing()
    original_size = image.GetSize()
    
    # 计算新的尺寸
    new_size = [
        int(round(original_size[0] * original_spacing[0] / target_spacing[0])),
        int(round(original_size[1] * original_spacing[1] / target_spacing[1])),
        int(round(original_size[2] * original_spacing[2] / target_spacing[2]))
    ]
    
    # 设置重采样参数
    resampler = sitk.ResampleImageFilter()
    resampler.SetOutputSpacing(target_spacing)
    resampler.SetSize(new_size)
    resampler.SetOutputDirection(image.GetDirection())
    resampler.SetOutputOrigin(image.GetOrigin())
    resampler.SetTransform(sitk.Transform())
    resampler.SetDefaultPixelValue(image.GetPixelIDValue())
    resampler.SetInterpolator(sitk.sitkBSpline)
    
    return resampler.Execute(image)

def normalize_intensity(image, window_min=-1000, window_max=500):
    """
    强度窗位调整和归一化
    
    Args:
        image: SimpleITK图像对象
        window_min: 窗位下限
        window_max: 窗位上限
        
    Returns:
        归一化后的图像
    """
    # 窗位调整
    windowed = sitk.IntensityWindowing(image, 
                                      windowMinimum=window_min,
                                      windowMaximum=window_max,
                                      outputMinimum=window_min,
                                      outputMaximum=window_max)
    
    return windowed

def standardize_orientation(image):
    """
    标准化图像方向到RAS
    
    Args:
        image: SimpleITK图像对象
        
    Returns:
        方向标准化后的图像
    """
    return sitk.DICOMOrient(image, 'RAS')

def preprocess_single_image(input_path, output_path, target_spacing=[0.703125, 0.703125, 1.25]):
    """
    预处理单个图像
    
    Args:
        input_path: 输入图像路径
        output_path: 输出图像路径
        target_spacing: 目标体素间距
    """
    print(f"处理图像: {input_path}")
    
    try:
        # 读取图像
        image = sitk.ReadImage(str(input_path))
        print(f"  原始尺寸: {image.GetSize()}")
        print(f"  原始间距: {image.GetSpacing()}")
        
        # 标准化方向
        image = standardize_orientation(image)
        print("  ✓ 方向标准化完成")
        
        # 重采样
        image = resample_image(image, target_spacing)
        print(f"  ✓ 重采样完成，新尺寸: {image.GetSize()}")
        
        # 强度归一化
        image = normalize_intensity(image)
        print("  ✓ 强度归一化完成")
        
        # 保存结果
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        sitk.WriteImage(image, str(output_path))
        print(f"  ✓ 保存至: {output_path}")
        
    except Exception as e:
        print(f"  ✗ 处理失败: {str(e)}")

def batch_preprocess(input_dir, output_dir, target_spacing=[0.703125, 0.703125, 1.25]):
    """
    批量预处理图像
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        target_spacing: 目标体素间距
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 查找所有NIfTI文件
    nifti_files = list(input_path.glob("*.nii")) + list(input_path.glob("*.nii.gz"))
    
    print(f"找到 {len(nifti_files)} 个NIfTI文件")
    
    for i, nifti_file in enumerate(nifti_files, 1):
        print(f"\n[{i}/{len(nifti_files)}]")
        
        # 构建输出路径
        output_file = output_path / nifti_file.name
        
        # 预处理
        preprocess_single_image(nifti_file, output_file, target_spacing)
    
    print(f"\n批量预处理完成！共处理 {len(nifti_files)} 个文件")

def create_test_data_json(image_dir, output_file):
    """
    创建测试数据JSON文件
    
    Args:
        image_dir: 图像目录
        output_file: 输出JSON文件路径
    """
    image_path = Path(image_dir)
    nifti_files = list(image_path.glob("*.nii")) + list(image_path.glob("*.nii.gz"))
    
    test_data = []
    for nifti_file in nifti_files:
        test_data.append({
            "image": str(nifti_file.absolute())
        })
    
    import json
    with open(output_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print(f"测试数据JSON文件已创建: {output_file}")
    print(f"包含 {len(test_data)} 个图像")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CT图像预处理工具')
    parser.add_argument('--input', required=True, help='输入图像文件或目录')
    parser.add_argument('--output', required=True, help='输出图像文件或目录')
    parser.add_argument('--spacing', nargs=3, type=float, 
                       default=[0.703125, 0.703125, 1.25],
                       help='目标体素间距 [x y z]')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    parser.add_argument('--create-json', help='创建测试数据JSON文件路径')
    
    args = parser.parse_args()
    
    if args.batch:
        # 批量处理
        batch_preprocess(args.input, args.output, args.spacing)
        
        # 如果指定了JSON文件路径，创建测试数据JSON
        if args.create_json:
            create_test_data_json(args.output, args.create_json)
    else:
        # 单个文件处理
        preprocess_single_image(args.input, args.output, args.spacing)

if __name__ == "__main__":
    main()
