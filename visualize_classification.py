import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.patches import Rectangle
import argparse
from matplotlib.font_manager import FontProperties
import sys

# 设置中文字体
def setup_chinese_font():
    """配置支持中文显示的字体"""
    try:
        # 常见中文字体优先级列表
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'STSong', 'FangSong', 'KaiTi']
        
        # 配置matplotlib字体
        plt.rcParams['font.sans-serif'] = chinese_fonts + plt.rcParams['font.sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.family'] = 'sans-serif'
        
        # 尝试找到中文字体文件
        if sys.platform.startswith('win'):
            for font_path in ["C:/Windows/Fonts/simhei.ttf", "C:/Windows/Fonts/msyh.ttc"]:
                if os.path.exists(font_path):
                    return FontProperties(fname=font_path)
        
        # 尝试使用字体名称
        for font in chinese_fonts:
            try:
                prop = FontProperties(family=font)
                return prop
            except:
                continue
                
        return None
    except Exception as e:
        print(f"设置中文字体出错: {e}")
        return None

# 将数据归一化为0-255范围的uint8类型
def normalize_to_uint8(data):
    """将数据归一化为0-255范围的uint8类型"""
    data_min = np.min(data)
    data_max = np.max(data)
    if data_max > data_min:
        normalized = ((data - data_min) / (data_max - data_min) * 255).astype(np.uint8)
    else:
        normalized = np.zeros_like(data, dtype=np.uint8)
    return normalized

# 加载JSON结果
def load_json_results(json_file):
    with open(json_file, 'r') as f:
        return json.load(f)

# 创建三平面可视化
def create_classification_visualization(image_data, box, output_file, detection_score, malignancy_prob, is_malignant, nodule_idx, chinese_font_prop=None, lower_bound=-1000, upper_bound=100):
    """
    创建肺结节的三平面可视化图像，包含良恶性分类结果
    
    参数:
        image_data: 3D图像数据
        box: 边界框坐标 [x1, y1, z1, x2, y2, z2]
        output_file: 输出文件路径
        detection_score: 检测置信度
        malignancy_prob: 恶性概率
        is_malignant: 是否恶性
        nodule_idx: 结节索引
        chinese_font_prop: 中文字体属性
        lower_bound: 窗位下限
        upper_bound: 窗位上限
    """
    print(f"\n创建结节 {nodule_idx + 1} 的三平面可视化:")
    print(f"  - 边界框坐标: {box}")
    print(f"  - 检测置信度: {detection_score:.4f}")
    print(f"  - 恶性概率: {malignancy_prob:.4f}")
    print(f"  - 分类结果: {'恶性' if is_malignant else '良性'}")

    # 计算边界框中心
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    print(f"  - 边界框中心点: ({x_center}, {y_center}, {z_center})")

    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T
    coronal_slice = image_data[:, y_center, :].T
    sagittal_slice = image_data[x_center, :, :].T

    # 窗位调整和归一化
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)

    # 将图像归一化为0-255范围的uint8类型
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)

    # 创建图像
    fig = plt.figure(figsize=(15, 6))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])

    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')

    # 获取图像尺寸
    img_height_axial, img_width_axial = axial_slice_norm.shape

    # 轴状视图上的边界框
    x_min_axial = max(0, box[0])
    y_min_axial = max(0, box[1])
    width_axial = min(img_width_axial - 1, box[3]) - x_min_axial
    height_axial = min(img_height_axial - 1, box[4]) - y_min_axial

    # 根据良恶性结果选择边框颜色
    box_color = 'r' if is_malignant else 'g'
    
    rect_axial = Rectangle(
        (x_min_axial, y_min_axial), width_axial, height_axial,
        linewidth=0.5, edgecolor=box_color, facecolor='none'
    )
    ax1.add_patch(rect_axial)
    
    # 设置标题
    if chinese_font_prop:
        ax1.set_title(f'轴状视图 (z={z_center})', fontproperties=chinese_font_prop)
    else:
        ax1.set_title(f'Axial View (z={z_center})')
    
    ax1.axis('off')

    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')

    # 冠状视图上的边界框 - 交换X和Z的映射
    img_height_coronal, img_width_coronal = coronal_slice_norm.shape

    # 冠状视图中，交换X和Z的映射：X为横坐标，Z为纵坐标
    x_min_coronal = max(0, box[0])
    y_min_coronal = max(0, box[2])
    width_coronal = min(img_width_coronal - 1, box[3]) - x_min_coronal
    height_coronal = min(img_height_coronal - 1, box[5]) - y_min_coronal

    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
        linewidth=0.5, edgecolor=box_color, facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    
    # 设置标题
    if chinese_font_prop:
        ax2.set_title(f'冠状视图 (y={y_center})', fontproperties=chinese_font_prop)
    else:
        ax2.set_title(f'Coronal View (y={y_center})')
    
    ax2.axis('off')

    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')

    # 矢状视图上的边界框 - 交换Y和Z的映射
    img_height_sagittal, img_width_sagittal = sagittal_slice_norm.shape

    # 矢状视图中，交换Y和Z的映射：Y为横坐标，Z为纵坐标
    x_min_sagittal = max(0, box[1])
    y_min_sagittal = max(0, box[2])
    width_sagittal = min(img_width_sagittal - 1, box[4]) - x_min_sagittal
    height_sagittal = min(img_height_sagittal - 1, box[5]) - y_min_sagittal

    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
        linewidth=0.5, edgecolor=box_color, facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    
    # 设置标题
    if chinese_font_prop:
        ax3.set_title(f'矢状视图 (x={x_center})', fontproperties=chinese_font_prop)
    else:
        ax3.set_title(f'Sagittal View (x={x_center})')
    
    ax3.axis('off')

    # 添加置信度和分类信息
    if chinese_font_prop:
        malignant_text = "恶性" if is_malignant else "良性"
        title_text = f'肺结节 #{nodule_idx + 1} - 检测置信度: {detection_score:.4f} - 恶性概率: {malignancy_prob:.4f} - 分类结果: {malignant_text}'
        plt.suptitle(title_text, fontsize=16, fontproperties=chinese_font_prop)
    else:
        malignant_text = "Malignant" if is_malignant else "Benign"
        title_text = f'Lung Nodule #{nodule_idx + 1} - Detection Score: {detection_score:.4f} - Malignancy: {malignancy_prob:.4f} - Classification: {malignant_text}'
        plt.suptitle(title_text, fontsize=16)

    # 保存图像
    plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为顶部标题留出空间
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"  - 三平面可视化图像已保存至: {output_file}")
    return True

# 处理分类结果并生成可视化
def process_classification_results(classification_file, image_path=None, output_dir=None, max_nodules=5, threshold=0.0):
    """处理分类结果并生成可视化图像"""
    # 设置输出目录
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output", "classification_viz")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 加载分类结果
    print(f"加载分类结果文件: {classification_file}")
    try:
        classification_results = load_json_results(classification_file)
        results = classification_results.get("results", [])
        
        if not results:
            print("分类结果为空，请检查JSON文件")
            return
            
        # 获取第一个结果
        result = results[0]
        
        # 如果未指定图像路径，使用结果中的路径
        if image_path is None:
            image_path = result["image"]
            
        print(f"使用图像: {image_path}")
        
        # 获取结节信息
        nodules = result["nodules"]
        print(f"发现 {len(nodules)} 个结节")
        
        # 筛选结节：根据检测置信度阈值和最大数量
        filtered_nodules = []
        
        for nodule in sorted(nodules, key=lambda x: x["detection_score"], reverse=True):
            if nodule["detection_score"] >= threshold and len(filtered_nodules) < max_nodules:
                filtered_nodules.append(nodule)
                
        if not filtered_nodules:
            print(f"没有检测置信度大于 {threshold} 的结节")
            if nodules:
                # 如果没有高置信度的结节，使用置信度最高的那个
                max_score_idx = max(range(len(nodules)), key=lambda i: nodules[i]["detection_score"])
                filtered_nodules.append(nodules[max_score_idx])
                print(f"使用检测置信度最高的结节: {nodules[max_score_idx]['box']}, 置信度: {nodules[max_score_idx]['detection_score']:.4f}")
                
        if not filtered_nodules:
            print("没有可用的结节进行可视化")
            return
            
        # 加载图像数据
        if image_path.lower().endswith(('.nii', '.nii.gz')):
            try:
                import nibabel as nib
                img = nib.load(image_path)
                image_data = img.get_fdata()
                print(f"NIfTI图像形状: {image_data.shape}")
                
                # 获取中文字体
                chinese_font_prop = setup_chinese_font()
                
                # 处理每个结节
                for i, nodule in enumerate(filtered_nodules):
                    print(f"\n处理结节 #{i + 1}:")
                    box = nodule["box"]
                    detection_score = nodule["detection_score"]
                    malignancy_prob = nodule["malignancy_probability"]
                    is_malignant = nodule["is_malignant"]
                    
                    print(f"原始边界框: {box}")
                    
                    # 应用flip_xy变换
                    flip_xy_box = [
                        image_data.shape[0] - box[3],  # x1 = dim_x - x2
                        image_data.shape[1] - box[4],  # y1 = dim_y - y2
                        box[2],                       # z1
                        image_data.shape[0] - box[0],  # x2 = dim_x - x1
                        image_data.shape[1] - box[1],  # y2 = dim_y - y1
                        box[5]                        # z2
                    ]
                    print(f"变换后边界框: {flip_xy_box}")
                    
                    # 创建可视化
                    output_file = os.path.join(output_dir, f"nodule_{i + 1}_{'malignant' if is_malignant else 'benign'}.png")
                    create_classification_visualization(
                        image_data,
                        flip_xy_box,
                        output_file,
                        detection_score,
                        malignancy_prob,
                        is_malignant,
                        i,
                        chinese_font_prop,
                        lower_bound=-1000,
                        upper_bound=100
                    )
                
                print(f"\n处理完成！共生成 {len(filtered_nodules)} 个结节分类可视化图像。")
                print(f"输出目录: {output_dir}")
                
            except ImportError:
                print("错误: 请安装nibabel库以处理NIfTI文件")
                print("安装命令: pip install nibabel")
            except Exception as e:
                print(f"处理NIfTI文件时出错: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"不支持的图像格式: {image_path}")
            print("目前仅支持.nii和.nii.gz格式的图像文件")
            
    except Exception as e:
        print(f"处理分类结果时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="肺结节分类可视化工具")
    parser.add_argument("--classification", type=str, default="output/classification_results.json",
                        help="分类结果JSON文件路径")
    parser.add_argument("--image", type=str, help="图像文件路径(.nii或.nii.gz格式)")
    parser.add_argument("--output", type=str, help="输出目录路径")
    parser.add_argument("--max-nodules", type=int, default=5, help="最多显示的结节数量")
    parser.add_argument("--threshold", type=float, default=0.0, help="检测置信度阈值")

    args = parser.parse_args()

    # 处理分类结果
    process_classification_results(
        args.classification,
        args.image,
        args.output,
        args.max_nodules,
        args.threshold
    )

if __name__ == "__main__":
    main() 