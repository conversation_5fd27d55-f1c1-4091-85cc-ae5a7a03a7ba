# 肺结节影像组学特征提取专用配置文件
# 针对肺结节检测和分类项目优化的PyRadiomics配置
# 适用于CT图像中的肺结节特征提取

# PyRadiomics核心配置
radiomics:
  # 图像类型配置 - 针对肺结节CT图像优化
  imageTypes:
    # 原始图像 - 基础特征提取
    Original: {}
    
    # 拉普拉斯高斯滤波器 (LoG) - 检测不同尺度的结节结构
    # sigma值通过settings配置
    LoG: {}
    
    # 小波变换 - 多尺度纹理分析
    # 对肺结节的边缘和内部纹理敏感
    # 参数通过settings配置
    Wavelet: {}
    
    # 平方滤波器 - 增强高强度区域(实性结节)
    Square: {}
    
    # 平方根滤波器 - 增强低强度区域(磨玻璃结节)
    SquareRoot: {}
  
  # 特征类别配置 - 全面的肺结节特征集
  featureClasses:
    # 一阶统计特征 - 结节密度和强度分布
    firstorder: []
    
    # 三维形状特征 - 结节几何形态学特征
    shape: []
    
    # 灰度共生矩阵 - 结节内部纹理模式
    # 距离和角度参数通过settings配置
    glcm: []
    
    # 灰度游程矩阵 - 结节均匀性和粗糙度
    # 距离和角度参数通过settings配置
    glrlm: []
    
    # 灰度大小区域矩阵 - 结节内部区域分布
    glszm: []
    
    # 邻域灰度差矩阵 - 局部纹理变化
    ngtdm: []
    
    # 灰度依赖矩阵 - 像素依赖关系
    gldm: []
  
  # PyRadiomics设置参数 - 针对肺结节CT图像优化
  settings:
    # 灰度级量化 - 适合CT图像的HU值范围
    binWidth: 25  # 25 HU为单位，适合肺结节密度差异
    
    # 重采样参数 - 标准化体素大小
    resampledPixelSpacing: [1.0, 1.0, 1.0]  # 1mm等方体素
    interpolator: 'sitkBSpline'  # B样条插值，保持边缘细节
    
    # 归一化设置 - CT图像标准化
    normalize: true
    normalizeScale: 100  # 标准化到0-100范围
    
    # 异常值处理 - 去除CT图像中的极值
    removeOutliers: 3  # 3个标准差外的异常值
    
    # 掩码处理
    correctMask: true  # 校正掩码边界
    label: 1  # 使用标签值1
    
    # 几何容差
    geometryTolerance: 1e-3
    
    # 边界填充 - 确保边缘特征计算准确
    padDistance: 5
    
    # 强制3D分析 - 充分利用CT的三维信息
    force2D: false
    
    # 距离设置 - 适合结节纹理分析
    distances: [1, 2, 3]  # 1-3像素距离
    
    # 角度设置 - GLCM和GLRLM纹理分析的方向
    angles: [0, 45, 90, 135]  # 全方向分析，度为单位
    
    # LoG滤波器参数 - 针对肺结节典型大小(5-30mm)优化
    sigma: [1.0, 2.0, 3.0, 4.0, 5.0]  # 覆盖小到中等结节的多尺度分析
    
    # Wavelet滤波器参数 - 多尺度纹理分析
    start_level: 0  # 小波分解起始层级
    level: 2        # 小波分解层级数，增加层级以捕获更多细节
    wavelet: 'coif1'  # 适合医学图像的小波基

# 处理参数配置 - 针对肺结节批量处理优化
processing:
  # 并行处理 - 根据系统配置调整
  n_jobs: 6  # 适中的并行数，避免内存溢出
  
  # 输出格式
  output_format: ['csv', 'json']  # 同时输出便于分析的格式
  
  # 质量控制 - 优化的肺结节质量标准
  quality_check: true
  min_voxel_count: 8   # 最小2x2x2体素，适应更小的结节
  max_voxel_count: 33510  # 最大约30mm结节
  
  # 密度范围检查 - 肺结节典型密度范围
  intensity_range:
    min_hu: -1000  # 肺组织下限
    max_hu: 3000   # 骨密度上限
  
  # 日志设置
  log_level: 'INFO'
  
  # 内存管理 - 防止大结节导致内存溢出
  max_memory_usage: '12GB'
  
  # 缓存设置 - 加速重复处理
  enable_cache: true
  cache_dir: './cache/radiomics'

# 肺结节专用高级配置
advanced:
  # 特征选择 - 去除冗余特征
  feature_selection:
    enabled: true
    
    # 方差阈值 - 去除低变异特征
    variance_threshold: 0.05
    
    # 相关性阈值 - 去除高相关特征
    correlation_threshold: 0.90
    
    # 特征稳定性检查
    stability_check: true
  
  # 肺结节特定验证
  validation:
    # 检查图像和掩码一致性
    check_image_mask_consistency: true
    
    # 检查掩码连通性 - 确保结节完整
    check_mask_connectivity: true
    
    # 检查结节大小合理性
    check_nodule_size: true
    min_diameter_mm: 3  # 最小直径3mm
    max_diameter_mm: 30 # 最大直径30mm
    
    # 检查结节密度合理性
    check_nodule_density: true
    min_mean_hu: -800   # 磨玻璃结节下限
    max_mean_hu: 200    # 钙化结节上限
  
  # 错误处理 - 确保批量处理稳定性
  error_handling:
    continue_on_error: true
    save_error_log: true
    error_log_file: 'lung_nodule_extraction_errors.log'
    
    # 重试机制
    max_retries: 2
    retry_delay: 1  # 秒

# 肺结节项目专用输出配置
output:
  # 文件命名 - 便于识别和管理
  naming:
    feature_file_prefix: 'lung_nodule_radiomics'
    include_timestamp: true
    timestamp_format: '%Y%m%d_%H%M%S'
    include_config_hash: true  # 包含配置哈希值
  
  # 数据格式
  data_format:
    csv:
      encoding: 'utf-8-sig'
      separator: ','
      decimal: '.'
      float_format: '%.6f'  # 6位小数精度
    
    json:
      indent: 2
      ensure_ascii: false
      sort_keys: true  # 排序键名便于比较
  
  # 肺结节专用报告
  reports:
    # 特征描述报告
    generate_feature_description: true
    
    # 质量评估报告
    generate_quality_report: true
    
    # 结节统计报告
    generate_nodule_statistics: true
    
    # 特征分布可视化
    generate_visualization: true
    
    # 特征相关性分析
    generate_correlation_analysis: true
    
    # 特征重要性排序
    generate_feature_ranking: true

# 肺结节分类任务特定配置
classification_specific:
  # 良恶性分类相关特征权重
  malignancy_features:
    # 形状特征权重
    shape_weight: 1.2
    
    # 纹理特征权重
    texture_weight: 1.5
    
    # 密度特征权重
    density_weight: 1.0
  
  # 结节类型分类相关特征
  nodule_type_features:
    # 实性结节特征
    solid_features: ['firstorder_Mean', 'firstorder_Median', 'shape_Sphericity']
    
    # 磨玻璃结节特征
    ggn_features: ['firstorder_10Percentile', 'firstorder_Skewness', 'glcm_Contrast']
    
    # 混合性结节特征
    mixed_features: ['firstorder_Range', 'glrlm_RunVariance', 'glszm_ZoneVariance']

# 性能优化配置
performance:
  # 内存优化
  memory_optimization:
    # 分块处理大结节
    enable_chunking: true
    chunk_size: 64  # 64x64x64体素块
    
    # 及时释放内存
    garbage_collection: true
    gc_frequency: 10  # 每10个结节清理一次
  
  # 计算优化
  computation_optimization:
    # 使用快速算法
    use_fast_algorithms: true
    
    # 跳过计算量大的特征（如果需要）
    skip_expensive_features: false
    
    # 特征计算超时设置
    feature_timeout: 300  # 5分钟超时

# 调试和开发配置
debug:
  # 调试模式
  debug_mode: false
  
  # 详细日志
  verbose_logging: false
  
  # 保存中间结果
  save_intermediate_results: false
  
  # 特征计算时间统计
  time_feature_extraction: true
  
  # 内存使用监控
  monitor_memory_usage: true