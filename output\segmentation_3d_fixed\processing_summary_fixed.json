{"total_nodules_processed": 3, "confidence_threshold": 0.99, "segmentation_method": "adaptive_threshold", "results": [{"nodule_id": 1, "image_path": "D:\\AIlung_test\\CYJ\\cyj_lps_segmented_cropped_lung.nii.gz", "detection_score": 0.9985892176628113, "segmentation_stats": {"voxel_count": 458, "volume_mm3": 283.0352783203125, "mean_intensity": -505.35917030567686, "std_intensity": 65.83571200235826, "min_intensity": -635.5, "max_intensity": -275.5, "mask_shape": [36, 35, 28]}, "output_files": {"mask": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_1\\nodule_1_segmentation_mask.nii.gz", "volume": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_1\\nodule_1_volume.nii.gz", "metadata": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_1\\nodule_1_metadata.json"}}, {"nodule_id": 2, "image_path": "D:\\AIlung_test\\CYJ\\cyj_lps_segmented_cropped_lung.nii.gz", "detection_score": 0.9964507818222046, "segmentation_stats": {"voxel_count": 299, "volume_mm3": 184.77630615234375, "mean_intensity": -322.16053511705684, "std_intensity": 166.92815874397422, "min_intensity": -598.0, "max_intensity": 7.0, "mask_shape": [26, 26, 24]}, "output_files": {"mask": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_2\\nodule_2_segmentation_mask.nii.gz", "volume": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_2\\nodule_2_volume.nii.gz", "metadata": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_2\\nodule_2_metadata.json"}}, {"nodule_id": 3, "image_path": "D:\\AIlung_test\\CYJ\\cyj_lps_segmented_cropped_lung.nii.gz", "detection_score": 0.9900249242782593, "segmentation_stats": {"voxel_count": 55, "volume_mm3": 33.98895263671875, "mean_intensity": -381.71363636363634, "std_intensity": 164.05364573116304, "min_intensity": -781.5, "max_intensity": -36.0, "mask_shape": [24, 25, 23]}, "output_files": {"mask": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_3\\nodule_3_segmentation_mask.nii.gz", "volume": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_3\\nodule_3_volume.nii.gz", "metadata": "output/segmentation_3d_fixed\\cyj_lps_segmented_cropped_lung\\nodule_3\\nodule_3_metadata.json"}}]}