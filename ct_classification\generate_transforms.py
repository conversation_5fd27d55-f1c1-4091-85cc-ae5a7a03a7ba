import torch
import numpy as np
from monai.transforms import (
    Compose,
    EnsureChannelFirstd,
    EnsureTyped,
    LoadImaged,
    RandFlipd,
    RandRotate90d,
    RandSpatialCropd,
    Resized,
)


def generate_classification_train_transform(image_key, label_key, img_patch_size):
    """
    Args:
        image_key (str): Key for image data, e.g., "image".
        label_key (str): Key for label data.
        img_patch_size (tuple): Patch size as numpy array, e.g., (64, 64, 64) [z, y, x].
    Returns:
        Compose: A composed transform for training data.
    """
    train_transforms = Compose(
        [
            LoadImaged(image_key),
            EnsureChannelFirstd(image_key, label_key, channel_dim="no_channel"),
            RandFlipd(image_key, spatial_axis=0, prob=0.5),
            RandFlipd(image_key, spatial_axis=1, prob=0.5),
            RandFlipd(image_key, spatial_axis=2, prob=0.5),
            RandSpatialCropd(image_key, roi_size=(img_patch_size[0]-8, img_patch_size[1], img_patch_size[2]-8)),
            Resized(image_key, spatial_size=img_patch_size, mode="trilinear", align_corners=False),
            RandRotate90d(image_key, prob=0.5, spatial_axes=(0, 1)),
            RandRotate90d(image_key, prob=0.5, spatial_axes=(0, 2)),
            EnsureTyped((image_key, label_key)),
        ]
    )
    return train_transforms


def generate_classification_val_transform(image_key, label_key, img_patch_size):
    """
    Args:
        image_key (str): Key for image data, e.g., "image".
        label_key (str): Key for label data.
        img_patch_size (tuple): Patch size as numpy array, e.g., (64, 64, 64) [z, y, x].
    Returns:
        Compose: A composed transform for validation data.
    """
    val_transforms = Compose(
        [
            LoadImaged(image_key),
            EnsureChannelFirstd(image_key, channel_dim="no_channel"),
            Resized(image_key, spatial_size=img_patch_size, mode="trilinear", align_corners=False),
            EnsureTyped((image_key, label_key)),
        ]
    )
    return val_transforms


def generate_classification_test_transform(image_key, img_patch_size):
    """
    Args:
        image_key (str): Key for image data, e.g., "image".
        img_patch_size (tuple): Patch size as numpy array, e.g., (64, 64, 64) [z, y, x].
    Returns:
        Compose: A composed transform for test data.
    """
    test_transforms = Compose(
        [
            LoadImaged(image_key),
            EnsureChannelFirstd(image_key, channel_dim="no_channel"),
            Resized(image_key, spatial_size=img_patch_size, mode="trilinear", align_corners=False),
            EnsureTyped(image_key),
        ]
    )
    return test_transforms



def generate_detection_inference_transform(
    image_key,
    pred_box_key,
    pred_label_key,
    pred_score_key,
    gt_box_mode,
    intensity_transform=None,
    affine_lps_to_ras=False,
    amp=True,
):
    from monai.transforms import (
        LoadImaged,
        EnsureChannelFirstd,
        Orientationd,
        EnsureTyped,
        Invertd,
        DeleteItemsd,
    )
    # 暂时移除 ClipBoxToImaged 以避免参数错误
    # from monai.apps.detection.transforms.dictionary import ClipBoxToImaged

    inference_transforms = Compose(
        [
            LoadImaged(keys=[image_key]),
            EnsureChannelFirstd(keys=[image_key]),
            Orientationd(keys=[image_key], axcodes="RAS" if affine_lps_to_ras else "RAS", allow_missing_keys=True),
            intensity_transform if intensity_transform is not None else lambda x: x,
            EnsureTyped(keys=[image_key]),
        ],
        # 添加map_items=True以确保变换信息被正确跟踪
        map_items=True
    )

    post_transforms = Compose(
        [
            Invertd(
                keys=[image_key, pred_box_key],  # 保留对 pred_box 的反转
                transform=inference_transforms,
                orig_keys=[image_key, image_key],
                meta_keys=[f"{image_key}_meta_dict", f"{image_key}_meta_dict"],  # 为两个键都提供元数据
                nearest_interp=[False, True],  # 图像使用原始插值，框使用最近邻插值
                to_tensor=[True, True],  # 确保转换为张量
                allow_missing_keys=True,  # 抑制警告
            ),
            # 暂时注释掉 ClipBoxToImaged，直到确认正确参数名
            # ClipBoxToImaged(box_keys=pred_box_key, image_key=image_key, remove_empty=True),
            DeleteItemsd(keys=[image_key]),
        ]
    )

    return inference_transforms, post_transforms




def generate_detection_train_transform(
    image_key,
    box_key,
    label_key,
    gt_box_mode,
    intensity_transform=None,
    patch_size=None,
    batch_size=None,
    affine_lps_to_ras=True,
    amp=True,
):
    """
    生成目标检测的训练变换。
    参数：
        image_key (str): 输入图像的键名。
        box_key (str): 真值框的键名。
        label_key (str): 真值标签的键名。
        gt_box_mode (str): 真值框的模式（如 'xyxy', 'xywh'）。
        intensity_transform (callable, optional): 应用于图像的强度变换。
        patch_size (tuple, optional): 图像裁剪的块大小。
        batch_size (int, optional): 批次大小。
        affine_lps_to_ras (bool): 是否将仿射变换从 LPS 转换为 RAS 坐标系。
        amp (bool): 是否使用自动混合精度。
    返回：
        Compose: 训练变换组合。
    """
    from monai.transforms import (
        LoadImaged,
        EnsureChannelFirstd,
        Orientationd,
        EnsureTyped,
        RandFlipd,
        RandSpatialCropd,
    )
    from monai.apps.detection.transforms.dictionary import RandCropBoxByPosNegLabeld

    transforms = [
        LoadImaged(keys=[image_key]),
        EnsureChannelFirstd(keys=[image_key]),
        Orientationd(keys=[image_key], axcodes="RAS" if affine_lps_to_ras else "RAS"),  # 修改：避免 axcodes=None 导致冲突
        intensity_transform if intensity_transform is not None else lambda x: x,
        RandFlipd(keys=[image_key, box_key], spatial_axis=0, prob=0.5),
        RandFlipd(keys=[image_key, box_key], spatial_axis=1, prob=0.5),
        RandFlipd(keys=[image_key, box_key], spatial_axis=2, prob=0.5),
    ]

    if patch_size is not None:
        transforms.append(
            RandCropBoxByPosNegLabeld(
                keys=[image_key, box_key, label_key],
                label_key=label_key,
                spatial_size=patch_size,
                pos=1,
                neg=1,
                num_samples=batch_size if batch_size else 1,
            )
        )
    else:
        transforms.append(RandSpatialCropd(keys=[image_key], roi_size=-1))

    transforms.append(
        EnsureTyped(keys=[image_key])  # 修改：省略 data_type 参数以避免 dtype 对象问题
    )

    return Compose(transforms)


def generate_detection_val_transform(
    image_key,
    box_key,
    label_key,
    gt_box_mode,
    intensity_transform=None,
    affine_lps_to_ras=True,
    amp=True,
):
    """
    生成目标检测的验证变换。
    参数：
        image_key (str): 输入图像的键名。
        box_key (str): 真值框的键名。
        label_key (str): 真值标签的键名。
        gt_box_mode (str): 真值框的模式（如 'xyxy', 'xywh'）。
        intensity_transform (callable, optional): 应用于图像的强度变换。
        affine_lps_to_ras (bool): 是否将仿射变换从 LPS 转换为 RAS 坐标系。
        amp (bool): 是否使用自动混合精度。
    返回：
        Compose: 验证变换组合。
    """
    from monai.transforms import (
        LoadImaged,
        EnsureChannelFirstd,
        Orientationd,
        EnsureTyped,
    )

    val_transforms = Compose(
        [
            LoadImaged(keys=[image_key]),
            EnsureChannelFirstd(keys=[image_key]),
            Orientationd(keys=[image_key], axcodes="RAS" if affine_lps_to_ras else "RAS"),  # 修改：避免 axcodes=None 导致冲突
            intensity_transform if intensity_transform is not None else lambda x: x,
            EnsureTyped(keys=[image_key]),  # 修改：省略 data_type 参数以避免 dtype 对象问题
        ]
    )

    return val_transforms
