# -*- coding: utf-8 -*-
# 版权所有 (c) MONAI 联盟
# 根据 Apache License, Version 2.0（“许可证”）授权；
# 除非符合许可证要求，否则不得使用此文件。
# 您可以在以下地址获取许可证副本：
#     http://www.apache.org/licenses/LICENSE-2.0
# 除非适用法律要求或书面同意，否则根据许可证分发的软件
# 按“原样”分发，不附带任何明示或暗示的担保或条件。
# 请参阅许可证以了解具体的权限和限制。

import os
import sys
import json
import logging
import torch
import numpy as np
from monai.data import Dataset, DataLoader, load_decathlon_datalist
from monai.data.utils import no_collation
from monai.transforms import Compose, ScaleIntensityRanged, EnsureChannelFirstd, Orientationd, EnsureTyped, Invertd, DeleteItemsd

# 添加模块路径，类似于 testing.py
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)  # 添加当前目录
sys.path.append(os.path.join(current_dir, "..", "ct_classification"))  # 添加上级目录中的 ct_classification

# 现在可以导入 generate_transforms
from generate_transforms import generate_detection_inference_transform

def debug_transforms(data_item, inference_transforms, post_transforms):
    """
    调试变换管道，输出每个步骤的元数据和数据状态。

    参数：
        data_item (dict): 单个数据项，包含图像等信息。
        inference_transforms (Compose): 推理变换管道。
        post_transforms (Compose): 后处理变换管道。
    """
    print("\n=== 调试变换管道 ===")
    print("初始数据键:", list(data_item.keys()))
    print("image 键值:", data_item.get("image"))
    print("image 类型:", type(data_item.get("image", None)).__name__)

    # 检查 image 是否为字符串路径
    if isinstance(data_item.get("image"), str):
        print("image 是字符串路径，应用 inference_transforms。")
        if os.path.exists(data_item["image"]):
            print(f"文件 {data_item['image']} 存在。")
            try:
                transformed_data = inference_transforms(data_item)
            except Exception as e:
                print(f"应用 inference_transforms 时出错: {e}")
                transformed_data = data_item
        else:
            print(f"文件 {data_item['image']} 不存在，跳过加载。")
            transformed_data = data_item
    else:
        print("image 不是字符串路径，跳过 LoadImaged 相关变换。")
        transformed_data = data_item
        # 检查是否有元数据
        if hasattr(data_item["image"], "meta"):
            print("找到图像元数据:", data_item["image"].meta)
            transformed_data["image_meta_dict"] = data_item["image"].meta
        else:
            print("图像没有元数据。")

    print("\n应用 inference_transforms 后的数据键:", list(transformed_data.keys()))
    if "image" in transformed_data:
        print("图像形状:", transformed_data["image"].shape if isinstance(transformed_data["image"], torch.Tensor) else "非 Tensor 类型")
        print("图像元数据:", transformed_data.get("image_meta_dict", "无元数据"))

    # 模拟预测结果（仅用于测试 post_transforms）
    transformed_data["pred_box"] = torch.tensor([[10, 10, 10, 20, 20, 20]], dtype=torch.float32)
    transformed_data["pred_label"] = torch.tensor([1], dtype=torch.int64)
    transformed_data["pred_score"] = torch.tensor([0.9], dtype=torch.float32)

    # 应用后处理变换
    try:
        post_transformed_data = post_transforms(transformed_data)
        print("\n应用 post_transforms 后的数据键:", list(post_transformed_data.keys()))
        print("预测框:", post_transformed_data.get("pred_box", "无预测框"))
    except Exception as e:
        print(f"应用 post_transforms 时出错: {e}")
        post_transformed_data = transformed_data

    print("=== 调试结束 ===\n")

def main():
    # 设置日志
    logging.basicConfig(
        stream=sys.stdout,
        level=logging.INFO,
        format="[%(asctime)s.%(msecs)03d][%(levelname)5s](%(name)s) - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # 加载环境和配置（假设存在配置文件）
    env_file = "../config/environment.json"
    config_file = "../config/config_test.json"

    if not os.path.exists(env_file) or not os.path.exists(config_file):
        logging.warning("配置文件未找到，使用默认设置。")

        class Args:
            data_list_file_path = "path/to/data_list.json"
            data_base_dir = "path/to/data"
            gt_box_mode = "xyxy"
            score_thresh = 0.5
            nms_thresh = 0.5

        args = Args()
    else:
        env_dict = json.load(open(env_file, "r"))
        config_dict = json.load(open(config_file, "r"))

        class Args:
            pass

        args = Args()
        for k, v in env_dict.items():
            setattr(args, k, v)
        for k, v in config_dict.items():
            setattr(args, k, v)

    # 定义强度变换
    intensity_transform = ScaleIntensityRanged(
        keys=["image"],
        a_min=-1024,
        a_max=300.0,
        b_min=0.0,
        b_max=1.0,
        clip=True,
    )

    # 生成推理和后处理变换
    inference_transforms, post_transforms = generate_detection_inference_transform(
        image_key="image",
        pred_box_key="pred_box",
        pred_label_key="pred_label",
        pred_score_key="pred_score",
        gt_box_mode=args.gt_box_mode,
        intensity_transform=intensity_transform,
        affine_lps_to_ras=True,
        amp=True,
    )

    # 加载测试数据（假设有数据列表文件）
    try:
        inference_data = load_decathlon_datalist(
            args.data_list_file_path,
            is_segmentation=True,
            data_list_key="validation",
            base_dir=args.data_base_dir,
        )
        print("成功加载数据列表:", inference_data)
    except Exception as e:
        logging.warning(f"无法加载数据列表: {e}")
        # 使用虚拟数据进行测试，确保路径有效
        inference_data = [{"image": "D:/LungCT/01/awl.nii.gz"}]  # 使用 test.py 中成功的路径
        print("使用虚拟数据:", inference_data)

    # 创建数据集和数据加载器
    inference_ds = Dataset(
        data=inference_data[:1],  # 仅使用第一个数据项进行调试
        transform=inference_transforms,
    )
    inference_loader = DataLoader(
        inference_ds,
        batch_size=1,
        num_workers=0,  # 调试时设置为0以避免多线程问题
        pin_memory=False,
        collate_fn=no_collation,
    )

    # 遍历数据并调试变换
    logging.info("开始调试变换管道...")
    for batch in inference_loader:
        for data_item in batch:
            debug_transforms(data_item, inference_transforms, post_transforms)
        break  # 仅处理第一个批次

    logging.info("调试完成。")

if __name__ == "__main__":
    main()
