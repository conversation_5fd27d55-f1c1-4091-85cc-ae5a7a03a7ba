# 肺结节检测系统 - 快速启动指南

## 🚀 快速开始

### 1. 启动GUI
```bash
python start_gui.py
```
或
```bash
python integrated_lung_detection_gui.py
```

### 2. 基本检测流程

#### 步骤1：配置基本参数
1. 在"结节检测"选项卡中
2. 设置预训练模型路径（.pt文件）
3. 设置图像数据目录
4. 设置结果输出路径

#### 步骤2：选择数据
**方式A：直接选择文件**
1. 选择"直接选择文件"模式
2. 点击"添加文件"选择图像文件
3. 或点击"添加目录"选择包含图像的文件夹

**方式B：使用JSON列表**
1. 选择"加载JSON列表"模式
2. 选择包含图像路径的JSON文件

#### 步骤3：运行检测
1. 点击"验证配置"确保设置正确
2. 点击"开始检测"运行任务
3. 在日志窗口查看进度

#### 步骤4：查看结果
1. 切换到"结果查看"选项卡
2. 加载生成的结果文件
3. 查看检测统计和详细结果

## 📁 支持的文件格式

### 图像格式
- NIfTI: `.nii`, `.nii.gz`
- DICOM: `.dcm`
- MetaImage: `.mhd`
- NRRD: `.nrrd`

### 配置文件
- JSON格式的数据列表
- JSON格式的配置文件

## ⚙️ 参数说明

### 检测参数
- **置信度阈值**: 控制检测敏感度（0.01-0.1）
- **NMS阈值**: 控制重叠结节合并（0.1-0.5）
- **批处理大小**: 控制内存使用（1-8）
- **图像块大小**: 处理时的图像块尺寸

### 预设参数
- **高敏感度**: 低阈值，检测更多结节
- **平衡模式**: 默认参数，平衡敏感度和精确度
- **高精确度**: 高阈值，减少假阳性

## 🔧 功能模块

### 1. 结节检测
- 基于深度学习的肺结节检测
- 支持3D CT图像
- 输出结节位置、大小和置信度

### 2. 后处理
- **结节分割**: 3D结节边界分割
- **良恶性分类**: 多种分类模型
- **影像组学**: 特征提取和分析
- **结果可视化**: 生成可视化图像

### 3. 批量处理
- 支持批量文件处理
- 任务流水线执行
- 进度监控和状态显示

### 4. 结果分析
- 检测结果统计
- 置信度分布分析
- 详细结果查看
- 报告生成

## 📊 结果文件格式

### 检测结果JSON
```json
[
  {
    "image_path": "sample.nii.gz",
    "nodules": [
      {
        "center": [100, 100, 50],
        "size": [10, 10, 8],
        "score": 0.85
      }
    ]
  }
]
```

### 字段说明
- `image_path`: 图像文件路径
- `center`: 结节中心坐标 [x, y, z]
- `size`: 结节大小 [width, height, depth]
- `score`: 检测置信度 (0-1)

## 🛠️ 故障排除

### 常见问题

#### 1. GUI无法启动
- 检查Python版本（需要3.7+）
- 确保安装了tkinter包
- 运行 `python -m tkinter` 测试

#### 2. 检测任务失败
- 检查模型文件路径是否正确
- 确保图像文件格式支持
- 查看日志窗口的错误信息

#### 3. 文件路径问题
- 使用绝对路径
- 避免路径中包含中文字符
- 确保文件存在且可读

#### 4. 内存不足
- 减小批处理大小
- 减小图像块尺寸
- 关闭其他占用内存的程序

### 获取帮助
1. 查看日志窗口的详细信息
2. 检查"系统设置"选项卡中的环境信息
3. 参考完整的使用说明文档

## 💡 使用技巧

### 1. 提高检测效果
- 根据数据特点调整参数
- 使用合适的预设参数
- 确保图像质量良好

### 2. 批量处理优化
- 合理设置批处理大小
- 选择合适的任务组合
- 监控系统资源使用

### 3. 结果分析
- 结合统计信息和详细结果
- 使用可视化功能验证结果
- 导出报告进行进一步分析

## 📞 技术支持

如遇到问题，请：
1. 查看完整的使用说明文档
2. 检查系统环境和依赖
3. 查看日志文件获取详细错误信息

---

**版本**: v1.1  
**更新日期**: 2025-01-31  
**作者**: AI Assistant
