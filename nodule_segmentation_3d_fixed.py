#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本的肺结节三维分割脚本
解决空掩码问题，优化分割参数
"""

import os
import json
import numpy as np
import nibabel as nib
import argparse
from scipy import ndimage
from skimage import measure, morphology
from skimage.segmentation import watershed
try:
    from skimage.feature import peak_local_maxima
except ImportError:
    def peak_local_maxima(image, min_distance=1, threshold_abs=None, threshold_rel=None):
        from scipy.ndimage import maximum_filter
        local_maxima = maximum_filter(image, size=min_distance*2+1) == image
        if threshold_abs is not None:
            local_maxima = local_maxima & (image > threshold_abs)
        if threshold_rel is not None:
            threshold_value = threshold_rel * image.max()
            local_maxima = local_maxima & (image > threshold_value)
        coords = np.where(local_maxima)
        return tuple(zip(*coords)) if len(coords[0]) > 0 else []

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import warnings
warnings.filterwarnings('ignore')


class NoduleSegmentation3DFixed:
    """
    修复版本的肺结节三维分割和重建类
    
    主要改进:
    1. 更宽松的阈值设置
    2. 更小的形态学操作核
    3. 多种分割策略的fallback机制
    4. 更详细的调试信息
    """
    
    def __init__(self, confidence_threshold=0.5, segmentation_method='adaptive_threshold'):
        """
        初始化分割器
        
        Args:
            confidence_threshold: 结节检测置信度阈值
            segmentation_method: 分割方法
        """
        self.confidence_threshold = confidence_threshold
        self.segmentation_method = segmentation_method
        
    def load_detection_results(self, results_file):
        """
        加载检测结果
        
        Args:
            results_file: 检测结果JSON文件路径
            
        Returns:
            dict: 检测结果数据
        """
        with open(results_file, 'r') as f:
            results = json.load(f)
        return results
    
    def load_image(self, image_path):
        """
        加载NIfTI图像
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            tuple: (图像数据, 仿射矩阵, 头信息)
        """
        img = nib.load(image_path)
        data = img.get_fdata()
        affine = img.affine
        header = img.header
        return data, affine, header
    
    def adjust_box_coordinates(self, box, image_shape, z_offset=0):
        """
        调整边界框坐标以匹配NIfTI数据的正确方向
        参考generate_annotated_images.py中的_adjust_nifti_boxes函数
        
        Args:
            box: 原始边界框坐标 [x1, y1, z1, x2, y2, z2]
            image_shape: 影像数据形状 (x_dim, y_dim, z_dim)
            z_offset: Z轴偏移量
            
        Returns:
            list: 调整后的边界框坐标
        """
        x1, y1, z1, x2, y2, z2 = box
        x_dim, y_dim, z_dim = image_shape
        
        # 翻转x和y坐标（参考generate_annotated_images.py的实现）
        x1_adj = x_dim - x1 - 1
        x2_adj = x_dim - x2 - 1
        y1_adj = y_dim - y1 - 1
        y2_adj = y_dim - y2 - 1
        
        # 调整z坐标
        z1_adj = z1 + z_offset
        z2_adj = z2 + z_offset
        
        # 确保坐标在有效范围内
        z1_adj = max(0, min(z1_adj, z_dim-1))
        z2_adj = max(0, min(z2_adj, z_dim-1))
        
        # 确保坐标顺序正确
        if x1_adj > x2_adj:
            x1_adj, x2_adj = x2_adj, x1_adj
        if y1_adj > y2_adj:
            y1_adj, y2_adj = y2_adj, y1_adj
        if z1_adj > z2_adj:
            z1_adj, z2_adj = z2_adj, z1_adj
            
        adjusted_box = [x1_adj, y1_adj, z1_adj, x2_adj, y2_adj, z2_adj]
        
        # 确保所有坐标在有效范围内
        adjusted_box[0] = max(0, min(adjusted_box[0], x_dim-1))  # x坐标
        adjusted_box[3] = max(0, min(adjusted_box[3], x_dim-1))
        adjusted_box[1] = max(0, min(adjusted_box[1], y_dim-1))  # y坐标
        adjusted_box[4] = max(0, min(adjusted_box[4], y_dim-1))
        adjusted_box[2] = max(0, min(adjusted_box[2], z_dim-1))  # z坐标
        adjusted_box[5] = max(0, min(adjusted_box[5], z_dim-1))
        
        print(f"坐标调整: 原始[{box}] -> 调整后[{adjusted_box}]")
        return adjusted_box
    
    def extract_nodule_roi(self, image_data, box, margin=10, apply_coordinate_adjustment=True):
        """
        提取结节感兴趣区域(ROI)，增强边界检查和错误处理
        
        Args:
            image_data: 3D图像数据
            box: 边界框坐标 [x1, y1, z1, x2, y2, z2]
            margin: 边界扩展像素数
            apply_coordinate_adjustment: 是否应用坐标调整（默认True）
            
        Returns:
            tuple: (ROI数据, ROI在原图中的坐标)
        """
        # 应用坐标调整以匹配正确的结节位置
        if apply_coordinate_adjustment:
            adjusted_box = self.adjust_box_coordinates(box, image_data.shape)
            x1, y1, z1, x2, y2, z2 = [int(coord) for coord in adjusted_box]
        else:
            x1, y1, z1, x2, y2, z2 = [int(coord) for coord in box]
        
        # 验证边界框的有效性
        if x1 >= x2 or y1 >= y2 or z1 >= z2:
            print(f"警告: 无效的边界框 [{x1},{y1},{z1},{x2},{y2},{z2}]")
            # 尝试修复边界框
            if x1 >= x2:
                x2 = x1 + 10
            if y1 >= y2:
                y2 = y1 + 10
            if z1 >= z2:
                z2 = z1 + 10
        
        # 确保边界框在图像范围内
        x1 = max(0, min(x1, image_data.shape[0] - 1))
        y1 = max(0, min(y1, image_data.shape[1] - 1))
        z1 = max(0, min(z1, image_data.shape[2] - 1))
        x2 = max(x1 + 1, min(x2, image_data.shape[0]))
        y2 = max(y1 + 1, min(y2, image_data.shape[1]))
        z2 = max(z1 + 1, min(z2, image_data.shape[2]))
        
        # 添加边界扩展
        x1_expanded = max(0, x1 - margin)
        y1_expanded = max(0, y1 - margin)
        z1_expanded = max(0, z1 - margin)
        x2_expanded = min(image_data.shape[0], x2 + margin)
        y2_expanded = min(image_data.shape[1], y2 + margin)
        z2_expanded = min(image_data.shape[2], z2 + margin)
        
        # 提取ROI
        roi_data = image_data[x1_expanded:x2_expanded, y1_expanded:y2_expanded, z1_expanded:z2_expanded].copy()
        roi_coords = (x1_expanded, y1_expanded, z1_expanded, x2_expanded, y2_expanded, z2_expanded)
        
        print(f"ROI提取: 原始框[{box}] -> 扩展框[{x1_expanded},{y1_expanded},{z1_expanded},{x2_expanded},{y2_expanded},{z2_expanded}]")
        print(f"ROI形状: {roi_data.shape}, 数值范围: [{roi_data.min():.2f}, {roi_data.max():.2f}]")
        
        # 检查ROI是否有效
        if roi_data.size == 0:
            print("错误: 提取的ROI为空")
            return None, None
        
        # 检查ROI数据的有效性
        unique_values = np.unique(roi_data)
        if len(unique_values) == 1:
            print(f"警告: ROI区域数值单一 (值: {unique_values[0]:.2f})，可能是空气区域或背景")
            # 尝试扩大ROI范围
            if margin < 20:
                print("尝试扩大ROI范围")
                return self.extract_nodule_roi(image_data, box, margin + 10, apply_coordinate_adjustment)
        
        return roi_data, roi_coords
    
    def adaptive_threshold_segmentation_fixed(self, roi_data, window_level=(-600, 1500)):
        """
        修复版本的自适应阈值分割方法
        
        Args:
            roi_data: ROI图像数据
            window_level: 窗位设置 (下限, 上限)
            
        Returns:
            numpy.ndarray: 分割掩码
        """
        print(f"开始自适应阈值分割，ROI形状: {roi_data.shape}")
        
        # 窗位调整
        windowed_data = np.clip(roi_data, window_level[0], window_level[1])
        print(f"窗位调整后范围: [{windowed_data.min():.2f}, {windowed_data.max():.2f}]")
        
        # 如果数据范围太小，尝试不同的窗位
        if windowed_data.max() - windowed_data.min() < 50:
            print("数据范围太小，尝试更宽的窗位")
            windowed_data = np.clip(roi_data, roi_data.min(), roi_data.max())
        
        # 计算多个阈值策略
        mean_intensity = np.mean(windowed_data)
        std_intensity = np.std(windowed_data)
        median_intensity = np.median(windowed_data)
        
        print(f"统计信息: 均值={mean_intensity:.2f}, 标准差={std_intensity:.2f}, 中位数={median_intensity:.2f}")
        
        # 尝试多个阈值
        thresholds = [
            mean_intensity + 0.2 * std_intensity,  # 更低的阈值
            mean_intensity + 0.3 * std_intensity,
            median_intensity + 0.2 * std_intensity,
            np.percentile(windowed_data, 75),  # 75百分位数
        ]
        
        best_mask = None
        best_voxel_count = 0
        
        for i, threshold in enumerate(thresholds):
            print(f"尝试阈值 {i+1}: {threshold:.2f}")
            
            # 二值化
            binary_mask = windowed_data > threshold
            voxel_count = np.sum(binary_mask)
            print(f"  二值化后体素数: {voxel_count}")
            
            if voxel_count == 0:
                continue
                
            # 更温和的形态学操作
            if voxel_count > 10:  # 只有足够的体素才进行形态学操作
                binary_mask = morphology.binary_opening(binary_mask, morphology.ball(1))  # 更小的核
                voxel_count_after_opening = np.sum(binary_mask)
                print(f"  开运算后体素数: {voxel_count_after_opening}")
                
                if voxel_count_after_opening > 0:
                    binary_mask = morphology.binary_closing(binary_mask, morphology.ball(2))  # 更小的核
                    voxel_count_after_closing = np.sum(binary_mask)
                    print(f"  闭运算后体素数: {voxel_count_after_closing}")
            
            # 连通域分析
            labeled_mask = measure.label(binary_mask)
            if labeled_mask.max() > 0:
                props = measure.regionprops(labeled_mask)
                print(f"  连通域数量: {len(props)}")
                
                if len(props) > 0:
                    largest_area = max(props, key=lambda x: x.area)
                    final_mask = (labeled_mask == largest_area.label)
                    final_voxel_count = np.sum(final_mask)
                    print(f"  最大连通域体素数: {final_voxel_count}")
                    
                    if final_voxel_count > best_voxel_count:
                        best_mask = final_mask
                        best_voxel_count = final_voxel_count
                        print(f"  -> 当前最佳结果")
        
        if best_mask is not None:
            print(f"最终选择的掩码体素数: {best_voxel_count}")
            return best_mask.astype(np.uint8)
        else:
            print("所有阈值都失败，返回空掩码")
            return np.zeros_like(roi_data, dtype=np.uint8)
    
    def simple_threshold_segmentation(self, roi_data):
        """
        简单阈值分割作为备用方案，增强对边缘情况的处理
        
        Args:
            roi_data: ROI图像数据
            
        Returns:
            numpy.ndarray: 分割掩码
        """
        print("使用简单阈值分割")
        
        # 检查数据有效性
        if roi_data is None or roi_data.size == 0:
            print("ROI数据无效")
            return np.zeros((1, 1, 1), dtype=np.uint8)
        
        unique_values = np.unique(roi_data)
        if len(unique_values) == 1:
            print(f"ROI数据值单一: {unique_values[0]:.2f}，无法进行分割")
            return np.zeros_like(roi_data, dtype=np.uint8)
        
        # 使用Otsu阈值
        from skimage.filters import threshold_otsu
        
        try:
            # 过滤极值，提高Otsu阈值的稳定性
            filtered_data = roi_data[(roi_data > np.percentile(roi_data, 1)) & 
                                   (roi_data < np.percentile(roi_data, 99))]
            
            if len(filtered_data) > 10:
                threshold = threshold_otsu(filtered_data)
                print(f"Otsu阈值: {threshold:.2f}")
                
                binary_mask = roi_data > threshold
                voxel_count = np.sum(binary_mask)
                print(f"Otsu分割体素数: {voxel_count}")
                
                if voxel_count > 0:
                    return binary_mask.astype(np.uint8)
        except Exception as e:
            print(f"Otsu阈值计算失败: {str(e)}")
        
        # 如果Otsu失败，使用百分位数阈值
        percentiles = [55, 60, 65, 70, 75, 80, 85, 90]
        for p in percentiles:
            threshold = np.percentile(roi_data, p)
            binary_mask = roi_data > threshold
            voxel_count = np.sum(binary_mask)
            print(f"{p}百分位阈值 {threshold:.2f}: {voxel_count} 体素")
            
            if voxel_count >= 5:  # 降低最小体素要求
                return binary_mask.astype(np.uint8)
        
        # 最后尝试：基于数据分布的自适应阈值
        try:
            data_mean = np.mean(roi_data)
            data_std = np.std(roi_data)
            
            # 尝试不同的标准差倍数
            for std_factor in [0.5, 1.0, 1.5, 2.0]:
                threshold = data_mean + std_factor * data_std
                binary_mask = roi_data > threshold
                voxel_count = np.sum(binary_mask)
                print(f"自适应阈值 (均值+{std_factor}*标准差={threshold:.2f}): {voxel_count} 体素")
                
                if 5 <= voxel_count <= roi_data.size * 0.8:  # 合理的体素数量范围
                    return binary_mask.astype(np.uint8)
        except Exception as e:
            print(f"自适应阈值计算失败: {str(e)}")
        
        print("所有阈值方法都失败")
        return np.zeros_like(roi_data, dtype=np.uint8)
    
    def segment_nodule(self, roi_data):
        """
        根据选择的方法分割结节，包含fallback机制和增强的错误处理
        
        Args:
            roi_data: ROI图像数据
            
        Returns:
            numpy.ndarray: 分割掩码
        """
        print(f"开始分割，方法: {self.segmentation_method}")
        
        # 检查输入数据有效性
        if roi_data is None:
            print("错误: ROI数据为空")
            return np.zeros((1, 1, 1), dtype=np.uint8)
        
        if roi_data.size == 0:
            print("错误: ROI数据大小为0")
            return np.zeros((1, 1, 1), dtype=np.uint8)
        
        # 检查数据范围
        unique_values = np.unique(roi_data)
        if len(unique_values) == 1:
            print(f"警告: ROI数据值单一 ({unique_values[0]:.2f})，尝试生成最小掩码")
            # 为单一值数据生成一个最小的中心掩码
            center_mask = np.zeros_like(roi_data, dtype=np.uint8)
            center_z, center_y, center_x = np.array(roi_data.shape) // 2
            # 创建一个小的中心区域
            z_start = max(0, center_z - 1)
            z_end = min(roi_data.shape[0], center_z + 2)
            y_start = max(0, center_y - 1)
            y_end = min(roi_data.shape[1], center_y + 2)
            x_start = max(0, center_x - 1)
            x_end = min(roi_data.shape[2], center_x + 2)
            center_mask[z_start:z_end, y_start:y_end, x_start:x_end] = 1
            print(f"生成中心掩码，体素数: {np.sum(center_mask)}")
            return center_mask
        
        # 首先尝试主要方法
        mask = None
        try:
            if self.segmentation_method == 'adaptive_threshold':
                mask = self.adaptive_threshold_segmentation_fixed(roi_data)
            else:
                # 其他方法保持原样
                mask = self.adaptive_threshold_segmentation_fixed(roi_data)
        except Exception as e:
            print(f"主要分割方法异常: {str(e)}")
            mask = None
        
        # 检查主要方法的结果
        if mask is None or np.sum(mask) == 0:
            print("主要分割方法失败，尝试备用方法")
            try:
                mask = self.simple_threshold_segmentation(roi_data)
            except Exception as e:
                print(f"备用分割方法异常: {str(e)}")
                mask = None
        
        # 如果所有方法都失败，生成一个最小掩码
        if mask is None or np.sum(mask) == 0:
            print("所有分割方法都失败，生成最小中心掩码")
            center_mask = np.zeros_like(roi_data, dtype=np.uint8)
            center_z, center_y, center_x = np.array(roi_data.shape) // 2
            # 创建一个更小的中心点
            if (0 <= center_z < roi_data.shape[0] and 
                0 <= center_y < roi_data.shape[1] and 
                0 <= center_x < roi_data.shape[2]):
                center_mask[center_z, center_y, center_x] = 1
                # 尝试扩展到3x3x3区域
                for dz in [-1, 0, 1]:
                    for dy in [-1, 0, 1]:
                        for dx in [-1, 0, 1]:
                            z, y, x = center_z + dz, center_y + dy, center_x + dx
                            if (0 <= z < roi_data.shape[0] and 
                                0 <= y < roi_data.shape[1] and 
                                0 <= x < roi_data.shape[2]):
                                center_mask[z, y, x] = 1
            mask = center_mask
            print(f"最终掩码体素数: {np.sum(mask)}")
        
        return mask
    
    def refine_segmentation(self, mask, roi_data):
        """
        精细化分割结果（更温和的处理）
        
        Args:
            mask: 初始分割掩码
            roi_data: ROI图像数据
            
        Returns:
            numpy.ndarray: 精细化后的分割掩码
        """
        if np.sum(mask) == 0:
            print("输入掩码为空，跳过精细化")
            return mask
        
        print(f"精细化前体素数: {np.sum(mask)}")
        
        # 只有足够大的掩码才进行精细化
        if np.sum(mask) > 20:
            # 更温和的形态学操作，使用3D结构元素
            refined_mask = morphology.binary_opening(mask, morphology.ball(1))  # 使用3D ball
            print(f"开运算后体素数: {np.sum(refined_mask)}")
            
            if np.sum(refined_mask) > 0:
                refined_mask = morphology.binary_closing(refined_mask, morphology.ball(1))
                print(f"闭运算后体素数: {np.sum(refined_mask)}")
                
                # 填充小孔洞
                refined_mask = ndimage.binary_fill_holes(refined_mask)
                print(f"填充孔洞后体素数: {np.sum(refined_mask)}")
            else:
                refined_mask = mask  # 如果开运算后为空，保持原始掩码
        else:
            refined_mask = mask  # 掩码太小，不进行精细化
        
        return refined_mask.astype(np.uint8)
    
    def create_3d_reconstruction(self, mask, roi_coords, original_shape):
        """
        创建三维重建数据
        
        Args:
            mask: 分割掩码
            roi_coords: ROI在原图中的坐标
            original_shape: 原始图像形状
            
        Returns:
            numpy.ndarray: 三维重建掩码（原图大小）
        """
        # 创建与原图同样大小的掩码
        full_mask = np.zeros(original_shape, dtype=np.uint8)
        
        # 将ROI掩码放回原图位置
        x1, y1, z1, x2, y2, z2 = roi_coords
        full_mask[x1:x2, y1:y2, z1:z2] = mask
        
        return full_mask
    
    def extract_nodule_volume(self, image_data, mask):
        """
        提取结节体积数据
        
        Args:
            image_data: 原始图像数据
            mask: 分割掩码
            
        Returns:
            numpy.ndarray: 结节体积数据
        """
        return image_data * mask
    
    def save_segmentation_results(self, segmented_nodule, original_affine, original_header, 
                                output_path, nodule_id, binary_mask=None):
        """
        保存分割结果
        
        Args:
            segmented_nodule: 分割后的结节数据
            original_affine: 原始仿射矩阵
            original_header: 原始头信息
            output_path: 输出路径
            nodule_id: 结节ID
            binary_mask: 二值掩码（可选，如果提供则直接使用）
            
        Returns:
            tuple: (掩码文件路径, 体积文件路径)
        """
        # 创建输出目录
        os.makedirs(output_path, exist_ok=True)
        
        # 保存分割掩码
        if binary_mask is not None:
            # 如果提供了二值掩码，直接使用
            mask = binary_mask.astype(np.uint8)
        else:
            # 修复原有逻辑：对于CT图像，HU值通常为负数
            # 不能简单使用 > 0 判断，应该使用 != 0
            mask = (segmented_nodule != 0).astype(np.uint8)
        
        mask_img = nib.Nifti1Image(mask, original_affine, original_header)
        mask_file = os.path.join(output_path, f"nodule_{nodule_id}_segmentation_mask.nii.gz")
        nib.save(mask_img, mask_file)
        
        # 保存结节体积
        volume_img = nib.Nifti1Image(segmented_nodule, original_affine, original_header)
        volume_file = os.path.join(output_path, f"nodule_{nodule_id}_volume.nii.gz")
        nib.save(volume_img, volume_file)
        
        print(f"分割结果已保存:")
        print(f"  掩码: {mask_file} (体素数: {np.sum(mask)})")
        print(f"  体积: {volume_file}")
        
        return mask_file, volume_file
    
    def save_metadata(self, metadata, output_path, nodule_id):
        """
        保存元数据
        
        Args:
            metadata: 元数据字典
            output_path: 输出路径
            nodule_id: 结节ID
            
        Returns:
            str: 元数据文件路径
        """
        metadata_file = os.path.join(output_path, f"nodule_{nodule_id}_metadata.json")
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"元数据已保存至: {metadata_file}")
        
        return metadata_file
    
    def calculate_segmentation_stats(self, mask, roi_data, spacing=None):
        """
        计算分割统计信息
        
        Args:
            mask: 分割掩码
            roi_data: ROI图像数据
            spacing: 像素间距
            
        Returns:
            dict: 统计信息
        """
        # 基本统计
        voxel_count = np.sum(mask)
        
        if spacing is not None:
            volume_mm3 = voxel_count * np.prod(spacing)
        else:
            volume_mm3 = voxel_count  # 假设单位体素
        
        # 强度统计
        nodule_intensities = roi_data[mask > 0]
        
        stats = {
            "voxel_count": int(voxel_count),
            "volume_mm3": float(volume_mm3),
            "mean_intensity": float(np.mean(nodule_intensities)) if len(nodule_intensities) > 0 else 0,
            "std_intensity": float(np.std(nodule_intensities)) if len(nodule_intensities) > 0 else 0,
            "min_intensity": float(np.min(nodule_intensities)) if len(nodule_intensities) > 0 else 0,
            "max_intensity": float(np.max(nodule_intensities)) if len(nodule_intensities) > 0 else 0,
            "mask_shape": list(mask.shape)
        }
        
        return stats
    
    def process_detection_results(self, detection_results_file, output_dir):
        """
        处理检测结果，对所有符合条件的结节进行分割
        
        Args:
            detection_results_file: 检测结果文件路径
            output_dir: 输出目录
            
        Returns:
            list: 处理结果列表
        """
        # 加载检测结果
        results = self.load_detection_results(detection_results_file)
        
        processed_results = []
        
        # 处理每个图像的检测结果
        for result in results.get("validation", []):
            image_path = result["image"]
            boxes = result["box"]
            scores = result["score"]
            
            print(f"\n处理图像: {image_path}")
            
            # 加载图像
            image_data, affine, header = self.load_image(image_path)
            
            # 获取像素间距
            spacing = None
            if hasattr(header, 'get_zooms'):
                spacing = header.get_zooms()[:3]
            
            # 处理每个结节
            for i, (box, score) in enumerate(zip(boxes, scores)):
                if score >= self.confidence_threshold:
                    print(f"\n=== 处理结节 {i+1}, 置信度: {score:.4f} ===")
                    
                    try:
                        # 提取ROI
                        roi_data, roi_coords = self.extract_nodule_roi(image_data, box)
                        
                        # 分割结节
                        mask = self.segment_nodule(roi_data)
                        
                        # 精细化分割
                        refined_mask = self.refine_segmentation(mask, roi_data)
                        
                        # 创建三维重建
                        full_mask = self.create_3d_reconstruction(refined_mask, roi_coords, image_data.shape)
                        
                        # 提取结节体积
                        nodule_volume = self.extract_nodule_volume(image_data, full_mask)
                        
                        # 计算统计信息
                        stats = self.calculate_segmentation_stats(refined_mask, roi_data, spacing)
                        
                        # 创建输出目录
                        image_name = os.path.splitext(os.path.basename(image_path))[0]
                        if image_name.endswith('.nii'):
                            image_name = image_name[:-4]
                        
                        nodule_output_dir = os.path.join(output_dir, image_name, f"nodule_{i+1}")
                        
                        # 保存分割结果
                        mask_file, volume_file = self.save_segmentation_results(
                            nodule_volume, affine, header, nodule_output_dir, i+1, full_mask
                        )
                        
                        # 准备元数据
                        metadata = {
                            "nodule_id": i+1,
                            "detection_info": {
                                "box": box,
                                "score": score,
                                "image_path": image_path
                            },
                            "segmentation_method": self.segmentation_method,
                            "segmentation_stats": stats,
                            "files": {
                                "segmentation_mask": os.path.basename(mask_file),
                                "nodule_volume": os.path.basename(volume_file)
                            }
                        }
                        
                        # 保存元数据
                        metadata_file = self.save_metadata(metadata, nodule_output_dir, i+1)
                        
                        # 添加到处理结果
                        processed_results.append({
                            "nodule_id": i+1,
                            "image_path": image_path,
                            "detection_score": score,
                            "segmentation_stats": stats,
                            "output_files": {
                                "mask": mask_file,
                                "volume": volume_file,
                                "metadata": metadata_file
                            }
                        })
                        
                        print(f"结节 {i+1} 处理完成，体素数: {stats['voxel_count']}")
                        
                    except Exception as e:
                        print(f"处理结节 {i+1} 时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        continue
                else:
                    print(f"跳过结节 {i+1}，置信度过低: {score:.4f}")
        
        return processed_results


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='修复版本的肺结节三维分割')
    parser.add_argument('--detection-results', required=True, help='检测结果JSON文件路径')
    parser.add_argument('--output-dir', required=True, help='输出目录')
    parser.add_argument('--confidence-threshold', type=float, default=0.6, help='置信度阈值')
    parser.add_argument('--segmentation-method', default='adaptive_threshold', 
                       choices=['adaptive_threshold'], help='分割方法')
    
    args = parser.parse_args()
    
    # 创建分割器
    segmenter = NoduleSegmentation3DFixed(
        confidence_threshold=args.confidence_threshold,
        segmentation_method=args.segmentation_method
    )
    
    # 处理检测结果
    print(f"开始处理检测结果: {args.detection_results}")
    print(f"输出目录: {args.output_dir}")
    print(f"置信度阈值: {args.confidence_threshold}")
    print(f"分割方法: {args.segmentation_method}")
    
    processed_results = segmenter.process_detection_results(
        args.detection_results, 
        args.output_dir
    )
    
    # 保存处理摘要
    summary = {
        "total_nodules_processed": len(processed_results),
        "confidence_threshold": args.confidence_threshold,
        "segmentation_method": args.segmentation_method,
        "results": processed_results
    }
    
    summary_file = os.path.join(args.output_dir, "processing_summary_fixed.json")
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n处理完成！")
    print(f"总共处理了 {len(processed_results)} 个结节")
    print(f"处理摘要已保存至: {summary_file}")
    
    # 统计成功分割的结节数量
    successful_segmentations = sum(1 for result in processed_results 
                                 if result['segmentation_stats']['voxel_count'] > 0)
    print(f"成功分割的结节数量: {successful_segmentations}/{len(processed_results)}")


if __name__ == '__main__':
    main()