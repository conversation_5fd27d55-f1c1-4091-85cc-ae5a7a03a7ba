#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本的肺结节可视化脚本
解决坐标系转换混乱问题，提供清晰的坐标处理逻辑

主要优化：
1. 统一坐标系转换逻辑
2. 增强边界框验证和修正
3. 改进三视图边界框绘制
4. 添加详细的调试信息
5. 提供多种坐标转换方法选择

Author: AI Assistant
Date: 2025-01-31
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.patches import Rectangle
import argparse
from matplotlib.font_manager import FontProperties
import sys

def setup_chinese_font():
    """配置支持中文显示的字体"""
    try:
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'STSong', 'FangSong', 'KaiTi']
        plt.rcParams['font.sans-serif'] = chinese_fonts + plt.rcParams['font.sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.family'] = 'sans-serif'
        
        if sys.platform.startswith('win'):
            for font_path in ["C:/Windows/Fonts/simhei.ttf", "C:/Windows/Fonts/msyh.ttc"]:
                if os.path.exists(font_path):
                    return FontProperties(fname=font_path)
        
        for font in chinese_fonts:
            try:
                prop = FontProperties(family=font)
                return prop
            except:
                continue
                
        return None
    except Exception as e:
        print(f"设置中文字体出错: {e}")
        return None

def normalize_to_uint8(data):
    """将数据归一化为0-255范围的uint8类型"""
    data_min = np.min(data)
    data_max = np.max(data)
    if data_max > data_min:
        normalized = ((data - data_min) / (data_max - data_min) * 255).astype(np.uint8)
    else:
        normalized = np.zeros_like(data, dtype=np.uint8)
    return normalized

def validate_and_fix_bbox(box, image_shape):
    """
    验证并修正边界框坐标
    
    Args:
        box: 边界框坐标 [x1, y1, z1, x2, y2, z2]
        image_shape: 图像形状 (x_dim, y_dim, z_dim)
        
    Returns:
        修正后的边界框坐标
    """
    x1, y1, z1, x2, y2, z2 = box
    x_dim, y_dim, z_dim = image_shape
    
    # 确保坐标顺序正确
    if x1 > x2:
        x1, x2 = x2, x1
    if y1 > y2:
        y1, y2 = y2, y1
    if z1 > z2:
        z1, z2 = z2, z1
    
    # 确保坐标在有效范围内
    x1 = max(0, min(x1, x_dim - 1))
    x2 = max(0, min(x2, x_dim - 1))
    y1 = max(0, min(y1, y_dim - 1))
    y2 = max(0, min(y2, y_dim - 1))
    z1 = max(0, min(z1, z_dim - 1))
    z2 = max(0, min(z2, z_dim - 1))
    
    return [x1, y1, z1, x2, y2, z2]

def apply_coordinate_transform(box, image_shape, method='flip_xy'):
    """
    应用坐标变换
    
    Args:
        box: 原始边界框坐标 [x1, y1, z1, x2, y2, z2]
        image_shape: 图像形状 (x_dim, y_dim, z_dim)
        method: 变换方法 ('flip_xy', 'flip_xyz', 'none')
        
    Returns:
        变换后的边界框坐标
    """
    x1, y1, z1, x2, y2, z2 = box
    x_dim, y_dim, z_dim = image_shape
    
    if method == 'flip_xy':
        # 翻转X和Y坐标（当前使用的方法）
        transformed_box = [
            x_dim - x2,  # x1 = dim_x - x2
            y_dim - y2,  # y1 = dim_y - y2
            z1,          # z1 保持不变
            x_dim - x1,  # x2 = dim_x - x1
            y_dim - y1,  # y2 = dim_y - y1
            z2           # z2 保持不变
        ]
    elif method == 'flip_xyz':
        # 翻转所有坐标
        transformed_box = [
            x_dim - x2,  # x1 = dim_x - x2
            y_dim - y2,  # y1 = dim_y - y2
            z_dim - z2,  # z1 = dim_z - z2
            x_dim - x1,  # x2 = dim_x - x1
            y_dim - y1,  # y2 = dim_y - y1
            z_dim - z1   # z2 = dim_z - z1
        ]
    elif method == 'none':
        # 不进行变换
        transformed_box = box.copy()
    else:
        raise ValueError(f"未知的变换方法: {method}")
    
    # 验证并修正变换后的坐标
    return validate_and_fix_bbox(transformed_box, image_shape)

def create_optimized_visualization(image_data, box, output_file, score, nodule_idx, 
                                 chinese_font_prop=None, lower_bound=-1000, upper_bound=100,
                                 debug_mode=True):
    """
    创建优化的肺结节三平面可视化图像
    
    Args:
        image_data: 3D图像数据
        box: 边界框坐标 [x1, y1, z1, x2, y2, z2] (已经过坐标变换)
        output_file: 输出文件路径
        score: 检测置信度
        nodule_idx: 结节索引
        chinese_font_prop: 中文字体属性
        lower_bound: 窗位下限
        upper_bound: 窗位上限
        debug_mode: 是否启用调试模式
    """
    if debug_mode:
        print(f"\n=== 创建结节 {nodule_idx + 1} 的优化三平面可视化 ===")
        print(f"图像形状: {image_data.shape}")
        print(f"边界框坐标: {box}")
        print(f"检测置信度: {score:.4f}")

    # 验证并修正边界框
    validated_box = validate_and_fix_bbox(box, image_data.shape)
    if validated_box != box:
        if debug_mode:
            print(f"边界框已修正: {box} -> {validated_box}")
        box = validated_box

    # 计算边界框中心
    x_center = int((box[0] + box[3]) / 2)
    y_center = int((box[1] + box[4]) / 2)
    z_center = int((box[2] + box[5]) / 2)
    
    if debug_mode:
        print(f"边界框中心点: ({x_center}, {y_center}, {z_center})")

    # 提取三个平面的切片
    axial_slice = image_data[:, :, z_center].T      # 轴状面 (z固定)
    coronal_slice = image_data[:, y_center, :].T    # 冠状面 (y固定)
    sagittal_slice = image_data[x_center, :, :].T   # 矢状面 (x固定)

    # 窗位调整
    if lower_bound is not None and upper_bound is not None:
        axial_slice = np.clip(axial_slice, lower_bound, upper_bound)
        coronal_slice = np.clip(coronal_slice, lower_bound, upper_bound)
        sagittal_slice = np.clip(sagittal_slice, lower_bound, upper_bound)

    # 归一化
    axial_slice_norm = normalize_to_uint8(axial_slice)
    coronal_slice_norm = normalize_to_uint8(coronal_slice)
    sagittal_slice_norm = normalize_to_uint8(sagittal_slice)

    # 创建图像
    fig = plt.figure(figsize=(15, 5))
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1])

    # ===== 轴状视图（Axial View - z固定）=====
    ax1 = plt.subplot(gs[0])
    ax1.imshow(axial_slice_norm, cmap='gray')

    # 轴状视图边界框 - 直接使用x,y坐标
    x_min_axial = max(0, box[0])
    y_min_axial = max(0, box[1])
    width_axial = max(1, box[3] - box[0])
    height_axial = max(1, box[4] - box[1])

    rect_axial = Rectangle(
        (x_min_axial, y_min_axial), width_axial, height_axial,
        linewidth=2, edgecolor='red', facecolor='none'
    )
    ax1.add_patch(rect_axial)
    
    if chinese_font_prop:
        ax1.set_title(f'轴状视图 (z={z_center})', fontproperties=chinese_font_prop, fontsize=12)
    else:
        ax1.set_title(f'Axial View (z={z_center})', fontsize=12)
    ax1.axis('off')

    # ===== 冠状视图（Coronal View - y固定）=====
    ax2 = plt.subplot(gs[1])
    ax2.imshow(coronal_slice_norm, cmap='gray')

    # 冠状视图边界框 - x为横坐标，z为纵坐标
    x_min_coronal = max(0, box[0])
    y_min_coronal = max(0, box[2])  # 使用z坐标作为纵坐标
    width_coronal = max(1, box[3] - box[0])
    height_coronal = max(1, box[5] - box[2])  # 使用z坐标差

    rect_coronal = Rectangle(
        (x_min_coronal, y_min_coronal), width_coronal, height_coronal,
        linewidth=2, edgecolor='red', facecolor='none'
    )
    ax2.add_patch(rect_coronal)
    
    if chinese_font_prop:
        ax2.set_title(f'冠状视图 (y={y_center})', fontproperties=chinese_font_prop, fontsize=12)
    else:
        ax2.set_title(f'Coronal View (y={y_center})', fontsize=12)
    ax2.axis('off')

    # ===== 矢状视图（Sagittal View - x固定）=====
    ax3 = plt.subplot(gs[2])
    ax3.imshow(sagittal_slice_norm, cmap='gray')

    # 矢状视图边界框 - y为横坐标，z为纵坐标
    x_min_sagittal = max(0, box[1])  # 使用y坐标作为横坐标
    y_min_sagittal = max(0, box[2])  # 使用z坐标作为纵坐标
    width_sagittal = max(1, box[4] - box[1])   # 使用y坐标差
    height_sagittal = max(1, box[5] - box[2])  # 使用z坐标差

    rect_sagittal = Rectangle(
        (x_min_sagittal, y_min_sagittal), width_sagittal, height_sagittal,
        linewidth=2, edgecolor='red', facecolor='none'
    )
    ax3.add_patch(rect_sagittal)
    
    if chinese_font_prop:
        ax3.set_title(f'矢状视图 (x={x_center})', fontproperties=chinese_font_prop, fontsize=12)
    else:
        ax3.set_title(f'Sagittal View (x={x_center})', fontsize=12)
    ax3.axis('off')

    # 添加总标题
    if chinese_font_prop:
        plt.suptitle(f'结节 #{nodule_idx+1} - 置信度: {score:.4f}', 
                    fontproperties=chinese_font_prop, fontsize=16)
    else:
        plt.suptitle(f'Nodule #{nodule_idx+1} - Confidence: {score:.4f}', fontsize=16)

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()

    if debug_mode:
        print(f"可视化图像已保存: {output_file}")

def load_json_predictions(json_file):
    """加载JSON预测结果"""
    with open(json_file, 'r') as f:
        return json.load(f)

def load_nifti_image(image_path):
    """加载NIfTI图像"""
    try:
        import nibabel as nib
        nii_img = nib.load(image_path)
        image_data = nii_img.get_fdata()
        affine = nii_img.affine
        return image_data, affine
    except ImportError:
        print("错误: 请安装nibabel库")
        print("安装命令: pip install nibabel")
        return None, None

def resolve_image_path(image_path, base_dir=None, debug_mode=True):
    """
    解析图像路径，处理相对路径和绝对路径

    Args:
        image_path: 原始图像路径
        base_dir: 基础目录（用于相对路径）
        debug_mode: 是否启用调试模式

    Returns:
        解析后的绝对路径，如果文件不存在则返回None
    """
    if debug_mode:
        print(f"解析图像路径: {image_path}")

    # 如果是绝对路径且文件存在，直接返回
    if os.path.isabs(image_path) and os.path.exists(image_path):
        if debug_mode:
            print(f"使用绝对路径: {image_path}")
        return image_path

    # 尝试相对于基础目录的路径
    if base_dir:
        full_path = os.path.join(base_dir, image_path)
        if os.path.exists(full_path):
            if debug_mode:
                print(f"使用基础目录路径: {full_path}")
            return full_path

    # 尝试相对于当前目录的路径
    if os.path.exists(image_path):
        abs_path = os.path.abspath(image_path)
        if debug_mode:
            print(f"使用当前目录路径: {abs_path}")
        return abs_path

    # 尝试只使用文件名在基础目录中查找
    if base_dir:
        filename = os.path.basename(image_path)
        full_path = os.path.join(base_dir, filename)
        if os.path.exists(full_path):
            if debug_mode:
                print(f"使用文件名匹配: {full_path}")
            return full_path

    if debug_mode:
        print(f"无法找到图像文件: {image_path}")
    return None

def visualize_nodules_optimized(predictions_file, image_path=None, output_dir="output/visualizations_optimized",
                               threshold=0.5, max_nodules=10, transform_method='flip_xy', debug_mode=True,
                               base_dir=None):
    """
    优化版本的结节可视化主函数

    Args:
        predictions_file: 预测结果JSON文件路径
        image_path: 图像文件路径（可选）
        output_dir: 输出目录
        threshold: 置信度阈值
        max_nodules: 最大结节数量
        transform_method: 坐标变换方法 ('flip_xy', 'flip_xyz', 'none')
        debug_mode: 是否启用调试模式
        base_dir: 图像文件基础目录（用于解析相对路径）
    """
    # 设置中文字体
    chinese_font_prop = setup_chinese_font()

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    if debug_mode:
        print(f"=== 优化版结节可视化开始 ===")
        print(f"预测文件: {predictions_file}")
        print(f"输出目录: {output_dir}")
        print(f"置信度阈值: {threshold}")
        print(f"坐标变换方法: {transform_method}")
        print(f"基础目录: {base_dir}")

    # 加载预测结果
    try:
        predictions = load_json_predictions(predictions_file)
        validation_predictions = predictions.get("validation", [])

        if not validation_predictions:
            print("预测结果为空，请检查JSON文件")
            return

        # 获取第一个预测结果
        pred = validation_predictions[0]

        # 如果未指定图像路径，使用预测结果中的路径
        if image_path is None:
            image_path = pred["image"]

        # 解析图像路径
        resolved_image_path = resolve_image_path(image_path, base_dir, debug_mode)
        if resolved_image_path is None:
            print(f"错误: 无法找到图像文件 {image_path}")
            if base_dir:
                print(f"基础目录: {base_dir}")
            print("请检查图像文件路径是否正确")
            return

        if debug_mode:
            print(f"使用图像: {resolved_image_path}")

        # 获取边界框和置信度
        boxes = pred["box"]
        scores = pred["score"]

        if debug_mode:
            print(f"发现 {len(boxes)} 个边界框")

        # 筛选边界框
        filtered_boxes = []
        filtered_scores = []

        for box, score in sorted(zip(boxes, scores), key=lambda x: x[1], reverse=True):
            if score >= threshold and len(filtered_boxes) < max_nodules:
                filtered_boxes.append(box)
                filtered_scores.append(score)

        if debug_mode:
            print(f"筛选后保留 {len(filtered_boxes)} 个高置信度边界框")

        if not filtered_boxes:
            print(f"没有找到置信度 >= {threshold} 的边界框")
            return

        # 加载图像
        if resolved_image_path.lower().endswith(('.nii', '.nii.gz')):
            image_data, affine = load_nifti_image(resolved_image_path)
            if image_data is None:
                return

            if debug_mode:
                print(f"成功加载NIfTI图像，形状: {image_data.shape}")

            # 处理每个边界框
            for i, (box, score) in enumerate(zip(filtered_boxes, filtered_scores)):
                if debug_mode:
                    print(f"\n--- 处理边界框 #{i + 1} ---")
                    print(f"原始边界框: {box}")

                # 应用坐标变换
                transformed_box = apply_coordinate_transform(box, image_data.shape, transform_method)

                if debug_mode:
                    print(f"变换后边界框: {transformed_box}")

                # 创建可视化
                output_file = os.path.join(output_dir, f"nodule_{i + 1}_optimized.png")
                create_optimized_visualization(
                    image_data,
                    transformed_box,
                    output_file,
                    score,
                    i,
                    chinese_font_prop,
                    lower_bound=-1000,
                    upper_bound=100,
                    debug_mode=debug_mode
                )

            if debug_mode:
                print(f"\n=== 处理完成！共生成 {len(filtered_boxes)} 个优化可视化图像 ===")
                print(f"输出目录: {output_dir}")

        else:
            print(f"不支持的图像格式: {resolved_image_path}")

    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        if debug_mode:
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='优化版肺结节可视化工具')
    parser.add_argument('--predictions', required=True, help='预测结果JSON文件路径')
    parser.add_argument('--image', help='图像文件路径（可选）')
    parser.add_argument('--output', default='output/visualizations_optimized', help='输出目录')
    parser.add_argument('--threshold', type=float, default=0.5, help='置信度阈值')
    parser.add_argument('--max-nodules', type=int, default=10, help='最大结节数量')
    parser.add_argument('--transform', choices=['flip_xy', 'flip_xyz', 'none'],
                       default='flip_xy', help='坐标变换方法')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--base-dir', help='图像文件基础目录（用于解析相对路径）')

    args = parser.parse_args()

    visualize_nodules_optimized(
        predictions_file=args.predictions,
        image_path=args.image,
        output_dir=args.output,
        threshold=args.threshold,
        max_nodules=args.max_nodules,
        transform_method=args.transform,
        debug_mode=args.debug,
        base_dir=args.base_dir
    )

if __name__ == "__main__":
    main()
