{"processing_summary": {"total_nodules": 3, "successful_extractions": 3, "failed_extractions": 0, "success_rate": 1.0}, "feature_summary": {"total_features_extracted": 2190}, "configuration": {"radiomics": {"imageTypes": {"Original": {}, "LoG": {}, "Wavelet": {}, "Square": {}, "SquareRoot": {}}, "featureClasses": {"firstorder": [], "shape": [], "glcm": [], "glrlm": [], "glszm": [], "ngtdm": [], "gldm": []}, "settings": {"binWidth": 25, "resampledPixelSpacing": [1.0, 1.0, 1.0], "interpolator": "sitkBSpline", "normalize": true, "normalizeScale": 100, "removeOutliers": 3, "correctMask": true, "label": 1, "geometryTolerance": "1e-3", "padDistance": 5, "force2D": false, "distances": [1, 2, 3], "angles": [0, 45, 90, 135], "sigma": [1.0, 2.0, 3.0, 4.0, 5.0], "start_level": 0, "level": 2, "wavelet": "coif1"}}, "processing": {"n_jobs": 6, "output_format": ["csv", "json"], "quality_check": true, "min_voxel_count": 8, "max_voxel_count": 33510, "intensity_range": {"min_hu": -1000, "max_hu": 3000}, "log_level": "INFO", "max_memory_usage": "12GB", "enable_cache": true, "cache_dir": "./cache/radiomics"}, "advanced": {"feature_selection": {"enabled": true, "variance_threshold": 0.05, "correlation_threshold": 0.9, "stability_check": true}, "validation": {"check_image_mask_consistency": true, "check_mask_connectivity": true, "check_nodule_size": true, "min_diameter_mm": 3, "max_diameter_mm": 30, "check_nodule_density": true, "min_mean_hu": -800, "max_mean_hu": 200}, "error_handling": {"continue_on_error": true, "save_error_log": true, "error_log_file": "lung_nodule_extraction_errors.log", "max_retries": 2, "retry_delay": 1}}, "output": {"naming": {"feature_file_prefix": "lung_nodule_radiomics", "include_timestamp": true, "timestamp_format": "%Y%m%d_%H%M%S", "include_config_hash": true}, "data_format": {"csv": {"encoding": "utf-8-sig", "separator": ",", "decimal": ".", "float_format": "%.6f"}, "json": {"indent": 2, "ensure_ascii": false, "sort_keys": true}}, "reports": {"generate_feature_description": true, "generate_quality_report": true, "generate_nodule_statistics": true, "generate_visualization": true, "generate_correlation_analysis": true, "generate_feature_ranking": true}}, "classification_specific": {"malignancy_features": {"shape_weight": 1.2, "texture_weight": 1.5, "density_weight": 1.0}, "nodule_type_features": {"solid_features": ["firstorder_Mean", "firstorder_Median", "shape_Sphericity"], "ggn_features": ["firstorder_10Percentile", "firstorder_Skewness", "glcm_Contrast"], "mixed_features": ["firstorder_Range", "glrlm_RunVariance", "glszm_ZoneVariance"]}}, "performance": {"memory_optimization": {"enable_chunking": true, "chunk_size": 64, "garbage_collection": true, "gc_frequency": 10}, "computation_optimization": {"use_fast_algorithms": true, "skip_expensive_features": false, "feature_timeout": 300}}, "debug": {"debug_mode": false, "verbose_logging": false, "save_intermediate_results": false, "time_feature_extraction": true, "monitor_memory_usage": true}}, "failed_cases": []}