#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肺结节检测任务运行脚本
简化检测任务的配置和执行流程

主要功能：
1. 自动配置检测环境
2. 验证模型和数据文件
3. 运行检测任务
4. 生成结果报告

Author: AI Assistant
Date: 2025-01-31
"""

import os
import sys
import json
import argparse
import subprocess
from pathlib import Path
import shutil

def check_environment():
    """检查运行环境"""
    print("=== 检查运行环境 ===")
    
    # 检查Python环境
    print(f"Python版本: {sys.version}")
    
    # 检查必要的库
    required_packages = ['torch', 'monai', 'nibabel', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少必要的包: {missing_packages}")
        print("请在medical_imaging_env环境中安装这些包")
        return False
    
    return True

def validate_paths(config):
    """验证配置文件中的路径"""
    print("\n=== 验证文件路径 ===")
    
    # 检查模型文件
    model_path = config.get('model_path')
    if not model_path or not os.path.exists(model_path):
        print(f"✗ 模型文件不存在: {model_path}")
        return False
    else:
        print(f"✓ 模型文件: {model_path}")
    
    # 检查数据列表文件
    data_list_path = config.get('data_list_file_path')
    if not data_list_path or not os.path.exists(data_list_path):
        print(f"✗ 数据列表文件不存在: {data_list_path}")
        return False
    else:
        print(f"✓ 数据列表文件: {data_list_path}")
    
    # 检查数据目录
    data_base_dir = config.get('data_base_dir')
    if not data_base_dir or not os.path.exists(data_base_dir):
        print(f"✗ 数据目录不存在: {data_base_dir}")
        return False
    else:
        print(f"✓ 数据目录: {data_base_dir}")
    
    # 检查数据列表中的图像文件
    try:
        with open(data_list_path, 'r') as f:
            data_list = json.load(f)
        
        if isinstance(data_list, list):
            for item in data_list:
                image_path = item.get('image')
                if image_path and os.path.exists(image_path):
                    print(f"✓ 图像文件: {image_path}")
                else:
                    print(f"✗ 图像文件不存在: {image_path}")
                    return False
    except Exception as e:
        print(f"✗ 读取数据列表文件失败: {e}")
        return False
    
    return True

def create_output_directory(result_path):
    """创建输出目录"""
    output_dir = os.path.dirname(result_path)
    os.makedirs(output_dir, exist_ok=True)
    print(f"✓ 输出目录: {output_dir}")

def run_detection_task(env_config_path, training_config_path):
    """运行检测任务"""
    print("\n=== 运行检测任务 ===")
    
    # 构建命令
    cmd = [
        sys.executable,
        "ct_detection/testing.py",
        "-e", env_config_path,
        "-c", training_config_path
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 运行检测
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✓ 检测任务完成")
            print("标准输出:")
            print(result.stdout)
        else:
            print("✗ 检测任务失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 运行检测任务时出错: {e}")
        return False
    
    return True

def generate_report(result_path):
    """生成检测结果报告"""
    print("\n=== 生成检测报告 ===")
    
    if not os.path.exists(result_path):
        print(f"✗ 结果文件不存在: {result_path}")
        return
    
    try:
        with open(result_path, 'r') as f:
            results = json.load(f)
        
        print(f"✓ 结果文件: {result_path}")
        
        # 分析结果
        if 'validation' in results:
            validation_results = results['validation']
            print(f"处理的图像数量: {len(validation_results)}")
            
            total_detections = 0
            for result in validation_results:
                image_path = result.get('image', 'Unknown')
                boxes = result.get('box', [])
                scores = result.get('score', [])
                
                print(f"\n图像: {os.path.basename(image_path)}")
                print(f"  检测到的结节数量: {len(boxes)}")
                
                if scores:
                    max_score = max(scores)
                    min_score = min(scores)
                    avg_score = sum(scores) / len(scores)
                    print(f"  置信度范围: {min_score:.3f} - {max_score:.3f}")
                    print(f"  平均置信度: {avg_score:.3f}")
                
                total_detections += len(boxes)
            
            print(f"\n总检测结节数: {total_detections}")
        
    except Exception as e:
        print(f"✗ 分析结果文件失败: {e}")

def setup_default_configs(image_path, model_path):
    """设置默认配置文件"""
    print("\n=== 设置默认配置 ===")
    
    # 创建测试数据JSON
    test_data = [{"image": str(Path(image_path).absolute())}]
    test_data_path = "ct_detection/test_data.json"
    
    with open(test_data_path, 'w') as f:
        json.dump(test_data, f, indent=2)
    print(f"✓ 创建测试数据文件: {test_data_path}")
    
    # 创建环境配置
    env_config = {
        "model_path": str(Path(model_path).absolute()),
        "data_list_file_path": str(Path(test_data_path).absolute()),
        "data_base_dir": str(Path(image_path).parent.absolute()),
        "result_list_file_path": str(Path("ct_detection/output/results.json").absolute())
    }
    
    env_config_path = "config/environment.json"
    os.makedirs("config", exist_ok=True)
    
    with open(env_config_path, 'w') as f:
        json.dump(env_config, f, indent=2)
    print(f"✓ 创建环境配置文件: {env_config_path}")
    
    # 使用默认训练配置
    training_config_path = "ct_detection/DukeLungRADS_BaseModel_epoch300_patch192x192y80z/training_config.json"
    if not os.path.exists(training_config_path):
        # 创建默认训练配置
        training_config = {
            "gt_box_mode": "cccwhd",
            "lr": 1e-2,
            "spacing": [0.703125, 0.703125, 1.25],
            "batch_size": 1,
            "patch_size": [192, 192, 80],
            "val_interval": 5,
            "val_batch_size": 1,
            "val_patch_size": [512, 512, 208],
            "fg_labels": [0],
            "n_input_channels": 1,
            "spatial_dims": 3,
            "score_thresh": 0.02,
            "nms_thresh": 0.22,
            "returned_layers": [1, 2],
            "conv1_t_stride": [2, 2, 1],
            "max_epoch": 300,
            "base_anchor_shapes": [[6, 8, 4], [8, 6, 5], [10, 10, 6]],
            "balanced_sampler_pos_fraction": 0.3,
            "resume_training": False,
            "resume_checkpoint_path": ""
        }
        
        os.makedirs(os.path.dirname(training_config_path), exist_ok=True)
        with open(training_config_path, 'w') as f:
            json.dump(training_config, f, indent=2)
        print(f"✓ 创建训练配置文件: {training_config_path}")
    
    return env_config_path, training_config_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='肺结节检测任务运行工具')
    parser.add_argument('--image', required=True, help='输入CT图像路径(.nii或.nii.gz)')
    parser.add_argument('--model', required=True, help='预训练模型路径(.pt)')
    parser.add_argument('--env-config', help='环境配置文件路径')
    parser.add_argument('--training-config', help='训练配置文件路径')
    parser.add_argument('--output', help='输出结果文件路径')
    parser.add_argument('--skip-check', action='store_true', help='跳过环境检查')
    
    args = parser.parse_args()
    
    print("肺结节检测任务运行工具")
    print("=" * 50)
    
    # 检查环境
    if not args.skip_check:
        if not check_environment():
            print("环境检查失败，请修复后重试")
            return
    
    # 设置配置文件
    if args.env_config and args.training_config:
        env_config_path = args.env_config
        training_config_path = args.training_config
    else:
        print("使用默认配置...")
        env_config_path, training_config_path = setup_default_configs(args.image, args.model)
    
    # 验证配置
    with open(env_config_path, 'r') as f:
        env_config = json.load(f)
    
    if not validate_paths(env_config):
        print("路径验证失败，请检查配置")
        return
    
    # 创建输出目录
    result_path = env_config.get('result_list_file_path', 'ct_detection/output/results.json')
    create_output_directory(result_path)
    
    # 运行检测任务
    if run_detection_task(env_config_path, training_config_path):
        # 生成报告
        generate_report(result_path)
        print(f"\n✓ 检测任务完成！结果保存在: {result_path}")
    else:
        print("\n✗ 检测任务失败")

if __name__ == "__main__":
    main()
