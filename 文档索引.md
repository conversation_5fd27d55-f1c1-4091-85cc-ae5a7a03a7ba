# 肺结节检测系统 - 文档索引

## 📚 文档结构

### 🎯 用户文档
- **[快速启动指南.md](快速启动指南.md)** - 快速上手指南，5分钟开始使用
- **[GUI_使用说明.md](GUI_使用说明.md)** - 详细的GUI界面使用说明
- **[项目结构说明.md](项目结构说明.md)** - 项目文件结构和组织说明

### 🔧 技术文档
- **[README/README.md](README/README.md)** - 项目主要说明文档
- **[README/README_env.MD](README/README_env.MD)** - 环境配置和依赖说明

### 📋 配置文件
- **[example_config.json](example_config.json)** - GUI配置文件示例
- **[radiomics_config_example.yaml](radiomics_config_example.yaml)** - 影像组学配置示例

## 🚀 快速导航

### 新用户
1. 阅读 [快速启动指南.md](快速启动指南.md)
2. 运行 `python start_gui.py` 启动GUI
3. 参考 [GUI_使用说明.md](GUI_使用说明.md) 了解详细功能

### 开发者
1. 查看 [项目结构说明.md](项目结构说明.md) 了解代码组织
2. 阅读 [README/README.md](README/README.md) 了解技术细节
3. 参考 [README/README_env.MD](README/README_env.MD) 配置开发环境

### 系统管理员
1. 参考 [README/README_env.MD](README/README_env.MD) 配置环境
2. 使用 [example_config.json](example_config.json) 作为配置模板
3. 查看 [项目结构说明.md](项目结构说明.md) 了解部署结构

## 📖 文档说明

### 文档特点
- **简洁明了**: 删除了冗余和过时的文档
- **结构清晰**: 按用途分类，便于查找
- **实用导向**: 重点关注实际使用和操作

### 文档维护
- 所有文档都是最新版本
- 与当前代码版本同步
- 定期更新和优化

## 🔍 查找信息

### 按功能查找
- **GUI使用** → [GUI_使用说明.md](GUI_使用说明.md)
- **快速开始** → [快速启动指南.md](快速启动指南.md)
- **环境配置** → [README/README_env.MD](README/README_env.MD)
- **项目结构** → [项目结构说明.md](项目结构说明.md)

### 按角色查找
- **用户** → 快速启动指南 + GUI使用说明
- **开发者** → 项目结构说明 + 主README
- **管理员** → 环境配置 + 配置示例

## 💡 使用建议

1. **首次使用**: 从快速启动指南开始
2. **深入了解**: 阅读GUI使用说明的完整功能介绍
3. **问题排查**: 查看各文档的故障排除部分
4. **定制配置**: 参考配置文件示例

## 📞 获取帮助

如果文档中没有找到需要的信息：
1. 检查GUI界面的"系统设置"选项卡
2. 查看日志文件获取详细错误信息
3. 参考配置文件示例进行对比

---

**文档版本**: v1.1  
**更新日期**: 2025-01-31  
**维护者**: AI Assistant
