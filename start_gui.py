#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肺结节检测系统GUI启动脚本

使用方法：
python start_gui.py

Author: AI Assistant
Date: 2025-01-31
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_environment():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    
    # 检查必要的包
    required_packages = ['tkinter', 'json', 'threading', 'subprocess', 'queue']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"错误: 缺少必要的包: {missing_packages}")
        return False
    
    print("✓ 环境检查通过")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("肺结节检测系统 - 集成GUI界面")
    print("=" * 60)
    print("版本: 1.0")
    print("作者: AI Assistant")
    print("日期: 2025-01-31")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        input("按回车键退出...")
        return
    
    try:
        # 导入GUI模块
        from integrated_lung_detection_gui import IntegratedLungDetectionGUI
        
        print("正在启动GUI界面...")
        
        # 创建根窗口
        root = tk.Tk()
        
        # 创建应用
        app = IntegratedLungDetectionGUI(root)
        
        print("GUI界面启动成功！")
        print("\n功能说明:")
        print("1. 结节检测 - 配置和运行肺结节检测任务")
        print("2. 后处理 - 结节分割、分类、特征提取等")
        print("3. 批量处理 - 批量处理多个文件")
        print("4. 结果查看 - 查看和分析检测结果")
        print("5. 系统设置 - 环境配置和系统信息")
        print("\n请在GUI界面中进行操作...")
        
        # 设置窗口关闭事件
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出肺结节检测系统吗？"):
                print("正在关闭GUI界面...")
                root.destroy()
                print("GUI界面已关闭")
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动GUI主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"错误: 无法导入GUI模块 - {e}")
        print("请确保integrated_lung_detection_gui.py文件存在且正确")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"错误: GUI启动失败 - {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
