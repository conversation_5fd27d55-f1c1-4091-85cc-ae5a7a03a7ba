#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目管理脚本 - 包含下一步操作、日志系统和项目精简建议

Author: AI Assistant
Date: 2025-01-08
"""

import os
import sys
import json
import logging
import datetime
from pathlib import Path
from typing import Dict, List, Tuple

class ProjectManager:
    """
    项目管理器 - 负责项目操作、日志管理和代码精简
    """
    
    def __init__(self, project_root: str = None):
        """
        初始化项目管理器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.log_dir = self.project_root / "logs"
        self.setup_logging()
        
    def setup_logging(self):
        """
        设置项目日志系统
        """
        # 创建日志目录
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        date_format = '%Y-%m-%d %H:%M:%S'
        
        # 创建日志文件名
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = self.log_dir / f"project_operations_{timestamp}.log"
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            datefmt=date_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('ProjectManager')
        self.logger.info(f"项目管理器初始化完成，日志文件: {log_file}")
        
    def get_next_steps(self) -> List[str]:
        """
        获取下一步操作命令
        
        Returns:
            命令列表
        """
        commands = [
            # 1. 修复掩码保存问题
            "python fix_mask_saving.py",
            
            # 2. 重新运行分割程序
            "python nodule_segmentation_3d_fixed.py",
            
            # 3. 验证掩码文件
            "python debug_mask_files.py",
            
            # 4. 运行特征提取
            "python radiomics_feature_extractor.py",
            
            # 5. 检查特征提取结果
            "python test_improved_extraction.py"
        ]
        
        self.logger.info("生成下一步操作命令列表")
        return commands
        
    def analyze_redundant_scripts(self) -> Dict[str, List[str]]:
        """
        分析项目中的冗余脚本
        
        Returns:
            分类后的脚本字典
        """
        # 核心功能脚本（保留）
        core_scripts = {
            "检测和分类": [
                "detect_and_classify.py",
                "ct_detection/test.py",
                "ct_detection/testing.py",
                "ct_classification/test.py"
            ],
            "分割功能": [
                "nodule_segmentation_3d_fixed.py",  # 主要分割脚本
            ],
            "特征提取": [
                "radiomics_feature_extractor.py",
                "test_improved_extraction.py"
            ],
            "可视化": [
                "generate_annotated_images.py",
                "visualize_classification.py"
            ],
            "项目管理": [
                "project_manager.py",  # 当前脚本
                "fix_mask_saving.py"   # 修复脚本
            ]
        }
        
        # 可能冗余的脚本
        redundant_scripts = {
            "旧版本分割脚本": [
                "nodule_segmentation_3d.py",  # 被fixed版本替代
                "nodule_segmentation_3d_fixed_backup.py"  # 备份文件
            ],
            "调试脚本（可选保留）": [
                "debug_mask_files.py",
                "debug_segmentation.py",
                "check_mask_simple.py"
            ],
            "分析和测试脚本（可选保留）": [
                "analyze_coordinates.py",
                "separate.py",
                "trans.py"
            ],
            "旧版本可视化脚本": [
                "visualize_nodules.py",  # 拼写错误的版本
                "visualize_nodules_simplified.py"  # 可能被其他版本替代
            ]
        }
        
        self.logger.info("完成项目脚本分析")
        return {"core": core_scripts, "redundant": redundant_scripts}
        
    def create_execution_script(self, commands: List[str]) -> str:
        """
        创建执行脚本
        
        Args:
            commands: 命令列表
            
        Returns:
            脚本文件路径
        """
        script_content = "@echo off\n"
        script_content += "echo 开始执行项目修复和验证流程...\n\n"
        
        for i, cmd in enumerate(commands, 1):
            script_content += f"echo 步骤 {i}: {cmd}\n"
            script_content += f"{cmd}\n"
            script_content += "if %errorlevel% neq 0 (\n"
            script_content += f"    echo 错误: 步骤 {i} 执行失败\n"
            script_content += "    pause\n"
            script_content += "    exit /b 1\n"
            script_content += ")\n\n"
            
        script_content += "echo 所有步骤执行完成！\n"
        script_content += "pause\n"
        
        script_file = self.project_root / "run_next_steps.bat"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
            
        self.logger.info(f"创建执行脚本: {script_file}")
        return str(script_file)
        
    def create_logging_wrapper(self, script_name: str) -> str:
        """
        为指定脚本创建带日志的包装器
        
        Args:
            script_name: 脚本名称
            
        Returns:
            包装器脚本路径
        """
        wrapper_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{script_name} 的日志包装器
自动生成于: {datetime.datetime.now()}
"""

import sys
import logging
import datetime
from pathlib import Path

# 设置日志
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
log_file = log_dir / f"{script_name.replace('.py', '')}_{timestamp}.log"

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('{script_name}')

try:
    logger.info(f"开始执行 {script_name}")
    
    # 导入并执行原始脚本
    import {script_name.replace('.py', '')}
    
    logger.info(f"{script_name} 执行完成")
except Exception as e:
    logger.error(f"{script_name} 执行失败: {{e}}")
    raise
'''
        
        wrapper_file = self.project_root / f"logged_{script_name}"
        with open(wrapper_file, 'w', encoding='utf-8') as f:
            f.write(wrapper_content)
            
        self.logger.info(f"创建日志包装器: {wrapper_file}")
        return str(wrapper_file)
        
    def generate_cleanup_recommendations(self) -> str:
        """
        生成清理建议报告
        
        Returns:
            报告文件路径
        """
        analysis = self.analyze_redundant_scripts()
        
        report_content = "# 项目精简建议报告\n\n"
        report_content += f"生成时间: {datetime.datetime.now()}\n\n"
        
        report_content += "## 核心脚本（建议保留）\n\n"
        for category, scripts in analysis["core"].items():
            report_content += f"### {category}\n"
            for script in scripts:
                report_content += f"- {script}\n"
            report_content += "\n"
            
        report_content += "## 可能冗余的脚本\n\n"
        for category, scripts in analysis["redundant"].items():
            report_content += f"### {category}\n"
            for script in scripts:
                if os.path.exists(self.project_root / script):
                    report_content += f"- ✅ {script} (存在)\n"
                else:
                    report_content += f"- ❌ {script} (不存在)\n"
            report_content += "\n"
            
        report_content += "## 清理建议\n\n"
        report_content += "1. **立即删除**: 备份文件和明确的旧版本\n"
        report_content += "2. **谨慎删除**: 调试脚本（在确认功能正常后）\n"
        report_content += "3. **评估后删除**: 分析脚本（根据实际需求）\n"
        report_content += "4. **重命名**: 拼写错误的文件\n\n"
        
        report_content += "## 推荐的删除命令\n\n"
        report_content += "```bash\n"
        report_content += "# 删除明确的冗余文件\n"
        report_content += "del nodule_segmentation_3d.py\n"
        report_content += "del nodule_segmentation_3d_fixed_backup.py\n"
        report_content += "del visualize_nodules.py\n"
        report_content += "```\n"
        
        report_file = self.project_root / "cleanup_recommendations.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
            
        self.logger.info(f"生成清理建议报告: {report_file}")
        return str(report_file)
        
    def run_complete_analysis(self):
        """
        运行完整的项目分析和管理
        """
        print("=" * 80)
        print("项目管理和精简分析")
        print("=" * 80)
        
        # 1. 生成下一步操作命令
        print("\n1. 下一步操作命令:")
        commands = self.get_next_steps()
        for i, cmd in enumerate(commands, 1):
            print(f"   {i}. {cmd}")
            
        # 2. 创建执行脚本
        script_file = self.create_execution_script(commands)
        print(f"\n2. 已创建执行脚本: {script_file}")
        
        # 3. 分析冗余脚本
        print("\n3. 项目脚本分析:")
        analysis = self.analyze_redundant_scripts()
        
        print("\n   核心脚本（保留）:")
        for category, scripts in analysis["core"].items():
            print(f"     {category}: {len(scripts)} 个脚本")
            
        print("\n   可能冗余的脚本:")
        total_redundant = 0
        for category, scripts in analysis["redundant"].items():
            existing_scripts = [s for s in scripts if os.path.exists(self.project_root / s)]
            total_redundant += len(existing_scripts)
            print(f"     {category}: {len(existing_scripts)} 个脚本")
            
        # 4. 生成清理建议
        report_file = self.generate_cleanup_recommendations()
        print(f"\n4. 已生成清理建议报告: {report_file}")
        
        print(f"\n总结:")
        print(f"   - 核心脚本: {sum(len(scripts) for scripts in analysis['core'].values())} 个")
        print(f"   - 可能冗余: {total_redundant} 个")
        print(f"   - 建议精简: {total_redundant} 个文件")
        
        print("\n下一步操作:")
        print("   1. 运行: run_next_steps.bat")
        print("   2. 查看: cleanup_recommendations.md")
        print("   3. 检查日志: logs/ 目录")

def main():
    """
    主函数
    """
    manager = ProjectManager()
    manager.run_complete_analysis()

if __name__ == "__main__":
    main()