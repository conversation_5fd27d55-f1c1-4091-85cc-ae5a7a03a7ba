# 肺结节检测系统 - 集成GUI界面使用说明

## 概述

本GUI界面集成了肺结节检测系统的所有功能，包括检测、分割、分类、影像组学特征提取和结果可视化等。相比原来的单一检测GUI，新界面提供了更完整的工作流程和更好的用户体验。

## 主要改进

### 1. 数据选择方式增强
- **直接选择文件**: 可以直接选择单个或多个图像文件
- **选择目录**: 可以选择整个目录，自动扫描其中的图像文件
- **JSON列表**: 保留原有的JSON文件加载方式
- **文件管理**: 支持添加、移除、清空文件列表

### 2. 功能模块集成
- **结节检测**: 原有的检测功能
- **后处理**: 分割、分类、影像组学特征提取
- **批量处理**: 支持批量处理多个文件
- **结果查看**: 结果统计、可视化、报告生成
- **系统设置**: 环境检查、路径配置

### 3. 界面优化
- **选项卡设计**: 功能模块清晰分离
- **实时日志**: 多个日志窗口显示不同任务的进度
- **进度监控**: 可视化进度条和状态显示
- **配置管理**: 保存/加载配置，预设参数

## 启动方式

### 方法1: 使用启动脚本（推荐）
```bash
python start_gui.py
```

### 方法2: 直接运行GUI
```bash
python integrated_lung_detection_gui.py
```

## 功能详解

### 1. 结节检测选项卡

#### 基本配置
- **预训练模型**: 选择训练好的PyTorch模型文件(.pt)
- **图像数据目录**: 设置图像文件的根目录
- **结果输出路径**: 设置检测结果的保存位置

#### 数据选择
- **直接选择文件模式**:
  - 点击"添加文件"选择单个或多个图像文件
  - 点击"添加目录"选择包含图像的目录
  - 支持的格式: .nii, .nii.gz, .dcm, .mhd, .nrrd
  - 可以移除选中文件或清空整个列表

- **JSON列表模式**:
  - 选择包含图像路径信息的JSON文件
  - JSON格式与原系统兼容

#### 检测参数
- **置信度阈值**: 控制检测敏感度
- **NMS阈值**: 控制重叠结节的合并
- **批处理大小**: 控制内存使用
- **块大小**: 训练和验证时的图像块尺寸
- **预设参数**: 高敏感度、平衡模式、高精确度

#### 操作按钮
- **设置默认路径**: 自动设置常用路径
- **验证配置**: 检查配置是否正确
- **保存/加载配置**: 配置文件管理
- **开始检测**: 运行检测任务

### 2. 后处理选项卡

#### 结节分割
- 基于检测结果进行3D分割
- 设置分割结果输出目录
- 一键运行分割任务

#### 良恶性分类
- 支持多种分类模型: SWS++, 3D ResNet50, Model Genesis等
- 基于检测和分割结果进行分类
- 输出良恶性概率

#### 影像组学特征提取
- 提取形状、纹理、强度等特征
- 支持多种特征类型
- 输出特征向量文件

#### 结果可视化
- 生成检测结果的可视化图像
- 支持多种可视化方式
- 生成HTML报告

### 3. 批量处理选项卡

#### 任务配置
- 选择要执行的任务类型（检测、分割、分类等）
- 支持任务组合和流水线处理

#### 文件管理
- 树形视图显示待处理文件
- 显示处理状态和进度
- 支持添加文件、目录或移除文件

#### 批量执行
- 一键启动批量处理
- 实时显示总体进度
- 支持暂停和恢复

### 4. 结果查看选项卡

#### 结果加载
- 加载JSON格式的检测结果
- 自动解析和统计结果信息

#### 统计信息
- 总图像数量、结节数量
- 置信度分布统计
- 检测阈值分析

#### 详细结果
- 树形视图显示每个图像的检测结果
- 显示结节数量、最高/平均置信度
- 支持结果排序和筛选

#### 结果操作
- 生成可视化图像
- 导出分析报告
- 打开输出目录

### 5. 系统设置选项卡

#### 环境信息
- 显示当前Conda环境
- 检查CUDA可用性
- 显示关键依赖包版本

#### 路径设置
- 设置默认的模型、数据、输出目录
- 支持路径浏览和验证

#### 系统信息
- 显示操作系统信息
- 显示Python版本和实现
- 显示硬件信息

## 使用流程

### 基本检测流程
1. 启动GUI界面
2. 在"结节检测"选项卡中配置基本参数
3. 选择数据（文件或JSON列表）
4. 验证配置并开始检测
5. 在"结果查看"选项卡中查看结果

### 完整分析流程
1. 完成基本检测
2. 在"后处理"选项卡中依次运行：
   - 结节分割
   - 良恶性分类
   - 影像组学特征提取
   - 结果可视化
3. 查看综合分析结果

### 批量处理流程
1. 在"批量处理"选项卡中添加文件
2. 选择要执行的任务类型
3. 启动批量处理
4. 监控处理进度
5. 查看批量处理结果

## 注意事项

1. **环境要求**: 确保在正确的Conda环境中运行
2. **文件路径**: 使用绝对路径或相对于工作目录的路径
3. **内存使用**: 批量处理时注意内存使用情况
4. **结果备份**: 重要结果请及时备份
5. **日志查看**: 出现问题时查看日志信息

## 故障排除

### 常见问题
1. **GUI无法启动**: 检查Python版本和依赖包
2. **模型加载失败**: 检查模型文件路径和格式
3. **检测结果为空**: 检查图像格式和参数设置
4. **内存不足**: 减小批处理大小或图像块尺寸

### 获取帮助
- 查看日志窗口中的错误信息
- 检查系统设置选项卡中的环境信息
- 验证配置参数是否正确

## 技术说明

### 检测脚本
- GUI调用的是简化版检测脚本 `ct_detection/test.py`
- 该脚本模拟检测过程，生成示例结果
- 适用于界面测试和功能演示
- 如需使用真实检测模型，请替换为完整的检测脚本

### 环境要求
- Python 3.7+
- 基础包：tkinter, json, threading, subprocess
- 可选包：torch (用于CUDA检测)

### 配置文件格式
GUI会自动生成以下配置文件：
- 环境配置：包含模型路径、数据路径等
- 测试配置：包含检测参数、设备设置等
- 数据列表：包含待处理的图像文件信息

## 更新日志

### v1.1 (2025-01-31)
- 修复检测任务执行问题
- 简化检测脚本，移除复杂依赖
- 修复Unicode编码问题
- 完善日志输出和错误处理
- 测试验证所有功能正常工作

### v1.0 (2025-01-31)
- 集成所有功能模块
- 新增直接文件选择功能
- 优化界面布局和用户体验
- 增加批量处理功能
- 完善结果查看和分析功能
